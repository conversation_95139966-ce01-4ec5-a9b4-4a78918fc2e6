/**
 * Quick Fix Script for Phantom Browser Browsing Issues
 * Resets problematic network configurations and clears settings
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Phantom Browser Browsing Issue Fix');
console.log('=====================================\n');

// 1. Clear stored settings that might have DoH enabled
const settingsPath = path.join(process.cwd(), 'settings.json');
if (fs.existsSync(settingsPath)) {
    try {
        fs.unlinkSync(settingsPath);
        console.log('✅ Cleared stored settings file');
    } catch (error) {
        console.log('⚠️ Could not clear settings file:', error.message);
    }
} else {
    console.log('ℹ️ No settings file found to clear');
}

// 2. Check for electron-store settings
const electronStoreDir = path.join(require('os').homedir(), 'AppData', 'Roaming', 'phantom-browser-working');
if (fs.existsSync(electronStoreDir)) {
    try {
        const configFile = path.join(electronStoreDir, 'config.json');
        if (fs.existsSync(configFile)) {
            fs.unlinkSync(configFile);
            console.log('✅ Cleared electron-store config file');
        }
    } catch (error) {
        console.log('⚠️ Could not clear electron-store config:', error.message);
    }
} else {
    console.log('ℹ️ No electron-store directory found');
}

// 3. Create a safe default configuration
const safeConfig = {
    privacy: {
        blockTrackers: true,
        blockAds: false,  // Disable ad blocking temporarily
        blockFingerprinting: false,  // Disable fingerprint protection temporarily
        spoofTimezone: false,
        spoofLanguage: false,
        spoofScreen: false,
        randomizeCanvasFingerprint: false,
        blockWebRTC: false,  // Disable WebRTC blocking temporarily
        clearCookiesOnExit: false,
        useDoH: false  // Ensure DoH is disabled
    },
    proxy: {
        currentProxy: null,
        proxies: []
    },
    security: {
        enableSandbox: true,
        blockMixedContent: false,  // Allow mixed content temporarily
        enableCSP: false  // Disable CSP temporarily
    }
};

try {
    fs.writeFileSync(settingsPath, JSON.stringify(safeConfig, null, 2));
    console.log('✅ Created safe default configuration');
} catch (error) {
    console.log('⚠️ Could not create safe config:', error.message);
}

console.log('\n📋 ISSUE ANALYSIS:');
console.log('==================');
console.log('🔍 Root Cause: DoH (DNS over HTTPS) configuration issue');
console.log('   - Invalid PAC script format was breaking all web requests');
console.log('   - DoH was enabled by default but not properly supported');
console.log('   - Network requests were being blocked by malformed proxy config');

console.log('\n🔧 FIXES APPLIED:');
console.log('=================');
console.log('✅ 1. Fixed NetworkConfigManager DoH implementation');
console.log('   - Replaced invalid PAC script with direct connection fallback');
console.log('   - Added proper error handling and logging');
console.log('   - DoH now falls back to direct connection for compatibility');

console.log('\n✅ 2. Updated default privacy settings');
console.log('   - Disabled DoH by default (useDoH: false)');
console.log('   - Maintained other privacy features');
console.log('   - Ensured backward compatibility');

console.log('\n✅ 3. Reset user configuration');
console.log('   - Cleared stored settings that might have DoH enabled');
console.log('   - Created safe default configuration');
console.log('   - Temporarily disabled some restrictive privacy features');

console.log('\n🚀 NEXT STEPS:');
console.log('==============');
console.log('1. Rebuild the application: npm run build');
console.log('2. Restart Phantom Browser: npm start');
console.log('3. Test browsing functionality');
console.log('4. Gradually re-enable privacy features if needed');

console.log('\n💡 RECOMMENDATIONS:');
console.log('===================');
console.log('• Start with minimal privacy settings and gradually increase');
console.log('• Test browsing after each privacy feature change');
console.log('• Use Direct connection mode initially');
console.log('• Check network status indicators in the privacy panel');
console.log('• Monitor console output for any remaining errors');

console.log('\n⚠️ TEMPORARY PRIVACY SETTINGS:');
console.log('==============================');
console.log('The following features are temporarily disabled for testing:');
console.log('• Ad blocking');
console.log('• Fingerprint protection');
console.log('• WebRTC blocking');
console.log('• Mixed content blocking');
console.log('• Content Security Policy');
console.log('• DNS over HTTPS');
console.log('\nYou can re-enable these one by one after confirming browsing works.');

console.log('\n✅ Fix script completed successfully!');
console.log('Please rebuild and restart the application.');
