directories:
  output: release
  buildResources: build
appId: com.privacy.phantom-browser
productName: Phantom Browser
files:
  - filter:
      - dist/**/*
      - renderer/**/*
      - node_modules/**/*
      - '!node_modules/.cache'
      - '!**/*.ts'
      - '!**/*.map'
extraResources:
  - from: docs/
    to: docs/
    filter:
      - '**/*'
mac:
  category: public.app-category.productivity
  icon: build/icon.icns
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
    - target: dir
      arch:
        - x64
  icon: build/icon.ico
  sign: false
  requestedExecutionLevel: asInvoker
linux:
  target: AppImage
  icon: build/icon.png
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: true
  installerIcon: build/icon.ico
  uninstallerIcon: build/icon.ico
  installerHeaderIcon: build/icon.ico
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Phantom Browser
portable:
  artifactName: PhantomBrowser-${version}-portable.exe
compression: maximum
removePackageScripts: true
electronVersion: 28.3.3
