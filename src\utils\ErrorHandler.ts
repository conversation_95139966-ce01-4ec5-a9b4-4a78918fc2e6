export interface ErrorResponse {
    success: false;
    error: string;
    details?: string;
    code?: string;
    timestamp?: number;
}

export interface SuccessResponse<T = any> {
    success: true;
    data?: T;
    message?: string;
    timestamp?: number;
}

export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse;

export enum ErrorCode {
    // Privacy errors
    PRIVACY_SETTINGS_LOAD_FAILED = 'PRIVACY_SETTINGS_LOAD_FAILED',
    PRIVACY_SETTINGS_SAVE_FAILED = 'PRIVACY_SETTINGS_SAVE_FAILED',
    CLEAR_BROWSING_DATA_FAILED = 'CLEAR_BROWSING_DATA_FAILED',
    
    // Security errors
    SECURITY_SETTINGS_LOAD_FAILED = 'SECURITY_SETTINGS_LOAD_FAILED',
    SECURITY_SETTINGS_SAVE_FAILED = 'SECURITY_SETTINGS_SAVE_FAILED',
    SECURITY_DATA_CLEAR_FAILED = 'SECURITY_DATA_CLEAR_FAILED',
    
    // Proxy errors
    PROXY_SETTINGS_LOAD_FAILED = 'PROXY_SETTINGS_LOAD_FAILED',
    PROXY_SETTINGS_SAVE_FAILED = 'PROXY_SETTINGS_SAVE_FAILED',
    PROXY_CONNECTION_FAILED = 'PROXY_CONNECTION_FAILED',
    PROXY_AUTH_FAILED = 'PROXY_AUTH_FAILED',
    
    // Search errors
    SEARCH_SETTINGS_LOAD_FAILED = 'SEARCH_SETTINGS_LOAD_FAILED',
    SEARCH_SETTINGS_SAVE_FAILED = 'SEARCH_SETTINGS_SAVE_FAILED',
    SEARCH_PROVIDER_INVALID = 'SEARCH_PROVIDER_INVALID',
    
    // Storage errors
    STORAGE_READ_FAILED = 'STORAGE_READ_FAILED',
    STORAGE_WRITE_FAILED = 'STORAGE_WRITE_FAILED',
    STORAGE_CORRUPTED = 'STORAGE_CORRUPTED',
    
    // Network errors
    NETWORK_CONNECTION_FAILED = 'NETWORK_CONNECTION_FAILED',
    NETWORK_TIMEOUT = 'NETWORK_TIMEOUT',
    
    // General errors
    INVALID_INPUT = 'INVALID_INPUT',
    PERMISSION_DENIED = 'PERMISSION_DENIED',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export class PhantomError extends Error {
    public readonly code: ErrorCode;
    public readonly details?: string;
    public readonly timestamp: number;

    constructor(code: ErrorCode, message: string, details?: string) {
        super(message);
        this.name = 'PhantomError';
        this.code = code;
        this.details = details;
        this.timestamp = Date.now();
    }

    toResponse(): ErrorResponse {
        return {
            success: false,
            error: this.message,
            details: this.details,
            code: this.code,
            timestamp: this.timestamp
        };
    }
}

export class ErrorHandler {
    private static instance: ErrorHandler;

    private constructor() {}

    public static getInstance(): ErrorHandler {
        if (!ErrorHandler.instance) {
            ErrorHandler.instance = new ErrorHandler();
        }
        return ErrorHandler.instance;
    }

    /**
     * Create a standardized error response
     */
    public createErrorResponse(error: unknown, code?: ErrorCode, details?: string): ErrorResponse {
        const timestamp = Date.now();

        if (error instanceof PhantomError) {
            return error.toResponse();
        }

        if (error instanceof Error) {
            return {
                success: false,
                error: this.getUserFriendlyMessage(error.message, code),
                details: details || error.message,
                code: code || ErrorCode.UNKNOWN_ERROR,
                timestamp
            };
        }

        return {
            success: false,
            error: this.getUserFriendlyMessage(String(error), code),
            details: details || String(error),
            code: code || ErrorCode.UNKNOWN_ERROR,
            timestamp
        };
    }

    /**
     * Create a standardized success response
     */
    public createSuccessResponse<T>(data?: T, message?: string): SuccessResponse<T> {
        return {
            success: true,
            data,
            message,
            timestamp: Date.now()
        };
    }

    /**
     * Wrap async operations with standardized error handling
     */
    public async handleAsync<T>(
        operation: () => Promise<T>,
        errorCode: ErrorCode,
        errorMessage?: string
    ): Promise<ApiResponse<T>> {
        try {
            const result = await operation();
            return this.createSuccessResponse(result);
        } catch (error) {
            console.error(`Operation failed [${errorCode}]:`, error);
            return this.createErrorResponse(error, errorCode, errorMessage);
        }
    }

    /**
     * Wrap sync operations with standardized error handling
     */
    public handleSync<T>(
        operation: () => T,
        errorCode: ErrorCode,
        errorMessage?: string
    ): ApiResponse<T> {
        try {
            const result = operation();
            return this.createSuccessResponse(result);
        } catch (error) {
            console.error(`Operation failed [${errorCode}]:`, error);
            return this.createErrorResponse(error, errorCode, errorMessage);
        }
    }

    /**
     * Convert technical error messages to user-friendly messages
     */
    private getUserFriendlyMessage(technicalMessage: string, code?: ErrorCode): string {
        // Map common technical errors to user-friendly messages
        const errorMappings: Record<string, string> = {
            'ENOENT': 'File or directory not found',
            'EACCES': 'Permission denied',
            'ENOTDIR': 'Invalid directory path',
            'EISDIR': 'Expected file but found directory',
            'EMFILE': 'Too many open files',
            'ENOSPC': 'Not enough disk space',
            'ETIMEDOUT': 'Operation timed out',
            'ECONNREFUSED': 'Connection refused',
            'ENOTFOUND': 'Network address not found',
            'EHOSTUNREACH': 'Host unreachable'
        };

        // Check for known technical error patterns
        for (const [pattern, friendlyMessage] of Object.entries(errorMappings)) {
            if (technicalMessage.includes(pattern)) {
                return friendlyMessage;
            }
        }

        // Code-specific user-friendly messages
        if (code) {
            const codeMappings: Partial<Record<ErrorCode, string>> = {
                [ErrorCode.PRIVACY_SETTINGS_LOAD_FAILED]: 'Failed to load privacy settings. Please restart the application.',
                [ErrorCode.PRIVACY_SETTINGS_SAVE_FAILED]: 'Failed to save privacy settings. Changes may not persist.',
                [ErrorCode.CLEAR_BROWSING_DATA_FAILED]: 'Failed to clear browsing data. Some data may remain.',
                [ErrorCode.SECURITY_SETTINGS_LOAD_FAILED]: 'Failed to load security settings. Using default settings.',
                [ErrorCode.SECURITY_SETTINGS_SAVE_FAILED]: 'Failed to save security settings. Changes may not persist.',
                [ErrorCode.PROXY_CONNECTION_FAILED]: 'Failed to connect to proxy server. Check proxy settings.',
                [ErrorCode.PROXY_AUTH_FAILED]: 'Proxy authentication failed. Check username and password.',
                [ErrorCode.SEARCH_PROVIDER_INVALID]: 'Invalid search provider configuration.',
                [ErrorCode.STORAGE_READ_FAILED]: 'Failed to read stored data. Settings may be reset.',
                [ErrorCode.STORAGE_WRITE_FAILED]: 'Failed to save data. Changes may not persist.',
                [ErrorCode.STORAGE_CORRUPTED]: 'Stored data is corrupted. Settings will be reset.',
                [ErrorCode.NETWORK_CONNECTION_FAILED]: 'Network connection failed. Check your internet connection.',
                [ErrorCode.INVALID_INPUT]: 'Invalid input provided. Please check your entries.',
                [ErrorCode.PERMISSION_DENIED]: 'Permission denied. Please check file permissions.'
            };

            if (codeMappings[code]) {
                return codeMappings[code]!;
            }
        }

        // If no mapping found, clean up the technical message
        return this.cleanTechnicalMessage(technicalMessage);
    }

    /**
     * Clean up technical error messages for user display
     */
    private cleanTechnicalMessage(message: string): string {
        // Remove file paths and technical details
        let cleaned = message
            .replace(/at .+:\d+:\d+/g, '') // Remove stack trace references
            .replace(/Error: /g, '') // Remove "Error: " prefix
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();

        // Capitalize first letter
        if (cleaned.length > 0) {
            cleaned = cleaned.charAt(0).toUpperCase() + cleaned.slice(1);
        }

        // Ensure it ends with a period
        if (cleaned && !cleaned.endsWith('.')) {
            cleaned += '.';
        }

        return cleaned || 'An unexpected error occurred.';
    }

    /**
     * Log error with context for debugging
     */
    public logError(error: unknown, context: string, additionalInfo?: Record<string, any>): void {
        const timestamp = new Date().toISOString();
        const errorInfo = {
            timestamp,
            context,
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : error,
            additionalInfo
        };

        console.error(`[${timestamp}] Error in ${context}:`, errorInfo);
    }

    /**
     * Validate input and throw PhantomError if invalid
     */
    public validateInput(condition: boolean, message: string, details?: string): void {
        if (!condition) {
            throw new PhantomError(ErrorCode.INVALID_INPUT, message, details);
        }
    }

    /**
     * Create a timeout wrapper for promises
     */
    public withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
        return Promise.race([
            promise,
            new Promise<never>((_, reject) => {
                setTimeout(() => {
                    reject(new PhantomError(
                        ErrorCode.NETWORK_TIMEOUT,
                        `Operation timed out after ${timeoutMs}ms`
                    ));
                }, timeoutMs);
            })
        ]);
    }
}

export default ErrorHandler;
