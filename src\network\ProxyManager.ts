import { session, app } from 'electron';
import { SocksClient } from 'socks';
import * as crypto from 'crypto';
import { SettingsStore } from '../utils/SettingsStore';

export interface ProxyConfig {
    type: 'http' | 'https' | 'socks4' | 'socks5' | 'direct';
    host: string;
    port: number;
    username?: string;
    password?: string;
    enabled: boolean;
    name?: string;
    description?: string;
}

export interface VPNConfig {
    provider: string;
    server: string;
    protocol: 'openvpn' | 'wireguard' | 'ikev2';
    credentials: {
        username?: string;
        password?: string;
        certificate?: string;
        privateKey?: string;
    };
    enabled: boolean;
}

export class ProxyManager {
    private currentProxy: ProxyConfig | null = null;
    private proxyList: ProxyConfig[] = [];
    private rotationEnabled: boolean = false;
    private rotationInterval: NodeJS.Timeout | null = null;
    private vpnConfig: VPNConfig | null = null;
    private settingsStore: SettingsStore;

    constructor() {
        this.settingsStore = SettingsStore.getInstance();
        this.loadSettings();
    }

    async initialize(): Promise<void> {
        // Initialization is now handled in constructor via loadSettings
        console.log('ProxyManager initialized');
    }

    private loadSettings(): void {
        try {
            const proxySettings = this.settingsStore.get('proxy');
            this.currentProxy = proxySettings.currentProxy;
            this.proxyList = proxySettings.proxyList;
            this.rotationEnabled = proxySettings.rotationEnabled;
            console.log('Proxy settings loaded from storage');
        } catch (error) {
            console.error('Failed to load proxy settings:', error);
            // Use default settings if loading fails
            this.initializeDefaultProxies();
        }
    }

    private saveSettings(): void {
        try {
            this.settingsStore.updateProxySettings({
                currentProxy: this.currentProxy,
                proxyList: this.proxyList,
                rotationEnabled: this.rotationEnabled,
                rotationInterval: 10 // Default interval
            });
            console.log('Proxy settings saved to storage');
        } catch (error) {
            console.error('Failed to save proxy settings:', error);
            throw new Error(`Failed to save proxy settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    private initializeDefaultProxies(): void {
        // Initialize with safe default proxies
        this.proxyList = [
            {
                type: 'direct',
                host: '',
                port: 0,
                enabled: true,
                name: 'Direct Connection',
                description: 'No proxy - direct internet connection'
            },
            // Add Tor proxy if available (common default)
            {
                type: 'socks5',
                host: '127.0.0.1',
                port: 9050,
                enabled: false,
                name: 'Tor (Local)',
                description: 'Local Tor SOCKS5 proxy'
            }
        ];
        this.currentProxy = null;
        this.rotationEnabled = false;
    }

    async setProxy(config: ProxyConfig): Promise<void> {
        this.currentProxy = config;

        if (config.type === 'direct') {
            await this.clearProxy();
            return;
        }

        const ses = session.defaultSession;

        try {
            let proxyRules = '';

            switch (config.type) {
                case 'http':
                case 'https':
                    proxyRules = `${config.type}=${config.host}:${config.port}`;
                    break;
                case 'socks4':
                case 'socks5':
                    proxyRules = `${config.type}=${config.host}:${config.port}`;
                    break;
            }

            await ses.setProxy({
                proxyRules,
                proxyBypassRules: 'localhost,127.0.0.1,<local>'
            });

            // Handle proxy authentication if credentials are provided
            if (config.username && config.password) {
                this.setupProxyAuthentication(config.username, config.password);
                console.log(`Proxy set with authentication: ${config.type}://${config.host}:${config.port}`);
            } else {
                console.log(`Proxy set: ${config.type}://${config.host}:${config.port}`);
            }

            // Save settings after successful proxy change
            this.saveSettings();
        } catch (error) {
            console.error('Failed to set proxy:', error);
            throw error;
        }
    }

    async clearProxy(): Promise<void> {
        const ses = session.defaultSession;
        await ses.setProxy({ proxyRules: 'direct://' });
        this.currentProxy = null;

        // Remove any existing authentication handlers
        app.removeAllListeners('login');

        // Save settings after clearing proxy
        this.saveSettings();

        console.log('Proxy cleared');
    }

    private setupProxyAuthentication(username: string, password: string): void {
        // Remove any existing login handlers to avoid duplicates
        app.removeAllListeners('login');

        // Set up authentication handler for proxy
        app.on('login', (event, webContents, authenticationResponseDetails, authInfo, callback) => {
            // Check if this is a proxy authentication request
            if (authInfo.isProxy) {
                event.preventDefault();
                console.log('Providing proxy authentication credentials');
                callback(username, password);
            } else {
                // For non-proxy authentication, let the default behavior handle it
                callback('', '');
            }
        });

        console.log('Proxy authentication handler configured');
    }

    async testProxy(config: ProxyConfig): Promise<boolean> {
        try {
            // Test proxy connectivity
            if (config.type === 'socks5' || config.type === 'socks4') {
                return await this.testSocksProxy(config);
            } else {
                return await this.testHttpProxy(config);
            }
        } catch (error) {
            console.error(`Proxy test failed for ${config.host}:${config.port}:`, error);
            return false;
        }
    }

    private async testSocksProxy(config: ProxyConfig): Promise<boolean> {
        try {
            // Simplified SOCKS proxy test
            return new Promise((resolve) => {
                const net = require('net');
                const socket = net.createConnection(config.port, config.host);

                socket.on('connect', () => {
                    socket.destroy();
                    resolve(true);
                });

                socket.on('error', () => {
                    resolve(false);
                });

                socket.setTimeout(5000, () => {
                    socket.destroy();
                    resolve(false);
                });
            });
        } catch {
            return false;
        }
    }

    private async testHttpProxy(config: ProxyConfig): Promise<boolean> {
        // Simplified HTTP proxy test
        // In a real implementation, you'd make an actual HTTP request through the proxy
        return new Promise((resolve) => {
            const net = require('net');
            const socket = net.createConnection(config.port, config.host);
            
            socket.on('connect', () => {
                socket.destroy();
                resolve(true);
            });
            
            socket.on('error', () => {
                resolve(false);
            });
            
            socket.setTimeout(5000, () => {
                socket.destroy();
                resolve(false);
            });
        });
    }

    async rotateProxy(): Promise<void> {
        const workingProxies: ProxyConfig[] = [];

        // Test all proxies and find working ones
        for (const proxy of this.proxyList) {
            if (proxy.enabled && await this.testProxy(proxy)) {
                workingProxies.push(proxy);
            }
        }

        if (workingProxies.length === 0) {
            console.warn('No working proxies found, using direct connection');
            await this.clearProxy();
            return;
        }

        // Select a random working proxy
        const randomProxy = workingProxies[Math.floor(Math.random() * workingProxies.length)];
        if (randomProxy) {
            await this.setProxy(randomProxy);
        }
    }

    enableProxyRotation(intervalMinutes: number = 10): void {
        this.rotationEnabled = true;
        
        if (this.rotationInterval) {
            clearInterval(this.rotationInterval);
        }

        this.rotationInterval = setInterval(async () => {
            await this.rotateProxy();
        }, intervalMinutes * 60 * 1000);

        console.log(`Proxy rotation enabled (${intervalMinutes} minutes interval)`);
    }

    disableProxyRotation(): void {
        this.rotationEnabled = false;
        
        if (this.rotationInterval) {
            clearInterval(this.rotationInterval);
            this.rotationInterval = null;
        }

        console.log('Proxy rotation disabled');
    }

    addProxy(config: ProxyConfig): void {
        this.proxyList.push(config);
        this.saveSettings();
    }

    removeProxy(host: string, port: number): void {
        this.proxyList = this.proxyList.filter(p => !(p.host === host && p.port === port));
        this.saveSettings();
    }

    getProxyList(): ProxyConfig[] {
        return [...this.proxyList];
    }

    getCurrentProxy(): ProxyConfig | null {
        return this.currentProxy ? { ...this.currentProxy } : null;
    }

    async setupDNSOverHTTPS(): Promise<void> {
        const ses = session.defaultSession;
        
        // Configure DNS over HTTPS
        await ses.setProxy({
            mode: 'pac_script',
            pacScript: `
                function FindProxyForURL(url, host) {
                    // Use DNS over HTTPS providers
                    if (isInNet(dnsResolve(host), "0.0.0.0", "0.0.0.0")) {
                        return "HTTPS *******:443; HTTPS *******:443; DIRECT";
                    }
                    return "DIRECT";
                }
            `
        });
    }

    async setupTrafficObfuscation(): Promise<void> {
        const ses = session.defaultSession;

        // Add random delays to requests to obfuscate traffic patterns
        ses.webRequest.onBeforeRequest({ urls: ['<all_urls>'] }, (details, callback) => {
            const delay = Math.random() * 100; // Random delay up to 100ms
            
            setTimeout(() => {
                callback({ cancel: false });
            }, delay);
        });

        // Add random headers to obfuscate requests
        ses.webRequest.onBeforeSendHeaders({ urls: ['<all_urls>'] }, (details, callback) => {
            const headers = details.requestHeaders;
            
            // Add random cache control
            headers['Cache-Control'] = Math.random() > 0.5 ? 'no-cache' : 'max-age=0';
            
            // Add random connection header
            headers['Connection'] = Math.random() > 0.5 ? 'keep-alive' : 'close';
            
            callback({ requestHeaders: headers });
        });
    }

    // VPN Integration (simplified)
    async connectVPN(config: VPNConfig): Promise<void> {
        this.vpnConfig = config;
        
        // In a real implementation, this would integrate with VPN clients
        // For now, we'll simulate VPN connection by setting up a proxy
        console.log(`VPN connection simulated: ${config.provider} - ${config.server}`);
    }

    async disconnectVPN(): Promise<void> {
        this.vpnConfig = null;
        await this.clearProxy();
        console.log('VPN disconnected');
    }

    getVPNStatus(): { connected: boolean; config?: VPNConfig } {
        return {
            connected: this.vpnConfig !== null,
            config: this.vpnConfig || undefined
        };
    }

    destroy(): void {
        if (this.rotationInterval) {
            clearInterval(this.rotationInterval);
            this.rotationInterval = null;
        }
    }
}
