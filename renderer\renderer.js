// Phantom Browser Renderer Script

// Search Engine Classes
class SearchEngine {
    constructor() {
        this.providers = new Map();
        this.settings = {
            defaultProvider: 'duckduckgo',
            enableSuggestions: true,
            enableHistory: true,
            maxSuggestions: 8,
            privacyMode: 'balanced',
            customProviders: []
        };
        this.searchHistory = [];
        this.maxHistorySize = 1000;
        this.initializeDefaultProviders();
    }

    initializeDefaultProviders() {
        const defaultProviders = [
            {
                id: 'duckduckgo',
                name: 'DuckDuckGo',
                baseUrl: 'https://duckduckgo.com',
                searchUrl: 'https://duckduckgo.com/?q={query}&t=phantom',
                privacyRating: 10,
                features: ['No tracking', 'No ads', 'Tor support', 'Instant answers'],
                description: 'Privacy-focused search engine with no tracking'
            },
            {
                id: 'startpage',
                name: 'Startpage',
                baseUrl: 'https://www.startpage.com',
                searchUrl: 'https://www.startpage.com/sp/search?query={query}&cat=web&pl=phantom',
                privacyRating: 9,
                features: ['Google results', 'No tracking', 'Anonymous view', 'EU based'],
                description: 'Private search with Google results, no tracking'
            },
            {
                id: 'brave',
                name: 'Brave Search',
                baseUrl: 'https://search.brave.com',
                searchUrl: 'https://search.brave.com/search?q={query}&source=phantom',
                privacyRating: 8,
                features: ['Independent index', 'No tracking', 'Ad-free', 'Fast results'],
                description: 'Independent search engine by Brave'
            },
            {
                id: 'searx',
                name: 'SearX',
                baseUrl: 'https://searx.org',
                searchUrl: 'https://searx.org/search?q={query}&categories=general',
                privacyRating: 10,
                features: ['Open source', 'No tracking', 'Aggregated results', 'Self-hostable'],
                description: 'Open source metasearch engine'
            }
        ];

        defaultProviders.forEach(provider => {
            this.providers.set(provider.id, provider);
        });
    }

    getProviders() {
        return Array.from(this.providers.values());
    }

    getProvider(id) {
        return this.providers.get(id);
    }

    getDefaultProvider() {
        return this.providers.get(this.settings.defaultProvider) || this.providers.get('duckduckgo');
    }

    setDefaultProvider(providerId) {
        if (this.providers.has(providerId)) {
            this.settings.defaultProvider = providerId;
        }
    }

    search(query, providerId) {
        const provider = providerId ? this.providers.get(providerId) : this.getDefaultProvider();
        if (!provider) {
            throw new Error('Search provider not found');
        }

        this.addToHistory(query, provider.id);
        const searchUrl = provider.searchUrl.replace('{query}', encodeURIComponent(query));
        console.log(`Searching "${query}" with ${provider.name}: ${searchUrl}`);
        return searchUrl;
    }

    async getSuggestions(query) {
        if (!this.settings.enableSuggestions || query.length < 2) {
            return [];
        }

        const suggestions = [];

        // Add history suggestions
        if (this.settings.enableHistory) {
            const historySuggestions = this.getHistorySuggestions(query);
            suggestions.push(...historySuggestions);
        }

        // Add mock suggestions
        const mockSuggestions = [
            `${query} tutorial`,
            `${query} guide`,
            `${query} examples`
        ];

        mockSuggestions.forEach(suggestion => {
            suggestions.push({
                query: suggestion,
                type: 'suggestion'
            });
        });

        return suggestions.slice(0, this.settings.maxSuggestions);
    }

    getHistorySuggestions(query) {
        const lowerQuery = query.toLowerCase();
        return this.searchHistory
            .filter(item => item.query.toLowerCase().includes(lowerQuery))
            .slice(0, 3)
            .map(item => ({
                query: item.query,
                type: 'history'
            }));
    }

    addToHistory(query, provider) {
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);
        this.searchHistory.unshift({
            query,
            timestamp: Date.now(),
            provider
        });

        if (this.searchHistory.length > this.maxHistorySize) {
            this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize);
        }
    }

    isValidUrl(input) {
        try {
            new URL(input);
            return true;
        } catch {
            const domainPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
            return domainPattern.test(input) || input.includes('.');
        }
    }

    processInput(input) {
        const trimmedInput = input.trim();

        if (this.isValidUrl(trimmedInput)) {
            const url = trimmedInput.startsWith('http') ? trimmedInput : `https://${trimmedInput}`;
            return { type: 'url', value: url };
        } else {
            const searchUrl = this.search(trimmedInput);
            return { type: 'search', value: searchUrl };
        }
    }

    getSettings() {
        return { ...this.settings };
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
    }
}

class PhantomBrowserUI {
    constructor() {
        this.currentTabId = 'default';
        this.tabs = new Map();
        this.privacyStats = {
            blockedTrackers: 0,
            blockedAds: 0,
            fingerprintAttempts: 0,
            dataLeaks: 0,
            decoyRequests: 0,
            obfuscatedPackets: 0
        };
        this.searchEngine = null;
        this.searchUI = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupPrivacyPanel();
        this.loadInitialSettings();
        this.startStatsUpdater();
        this.setupIpcListeners();
        this.initializeSearchEngine();
    }

    setupEventListeners() {
        // Navigation buttons
        document.getElementById('backBtn').addEventListener('click', () => {
            window.phantomAPI?.goBack();
        });

        document.getElementById('forwardBtn').addEventListener('click', () => {
            window.phantomAPI?.goForward();
        });

        document.getElementById('reloadBtn').addEventListener('click', () => {
            window.phantomAPI?.reload();
        });

        // Address bar - basic navigation (will be enhanced by search engine)
        const addressBar = document.getElementById('addressBar');
        if (addressBar) {
            // Only add basic keypress handler if search engine is not initialized
            this.addressBarHandler = (e) => {
                if (e.key === 'Enter' && !this.searchEngine) {
                    console.log('Basic navigation triggered:', addressBar.value);
                    this.navigate(addressBar.value);
                }
            };
            addressBar.addEventListener('keypress', this.addressBarHandler);
            console.log('Address bar event listener attached successfully');
        } else {
            console.error('Address bar element not found!');
        }

        // New tab button
        document.getElementById('newTabBtn').addEventListener('click', () => {
            this.createNewTab();
        });

        // Privacy panel toggle
        document.getElementById('privacyPanelBtn').addEventListener('click', () => {
            this.togglePrivacyPanel();
        });

        // User agent rotation
        document.getElementById('rotateUserAgentBtn').addEventListener('click', () => {
            this.rotateUserAgent();
        });

        // Clear browsing data button
        const clearDataBtn = document.getElementById('clearBrowsingDataBtn');
        if (clearDataBtn) {
            clearDataBtn.addEventListener('click', () => {
                this.clearBrowsingData();
            });
        }

        // Network status buttons
        const refreshNetworkStatusBtn = document.getElementById('refreshNetworkStatusBtn');
        if (refreshNetworkStatusBtn) {
            refreshNetworkStatusBtn.addEventListener('click', () => {
                this.refreshNetworkStatus();
            });
        }

        const configureNetworkBtn = document.getElementById('configureNetworkBtn');
        if (configureNetworkBtn) {
            configureNetworkBtn.addEventListener('click', () => {
                this.openNetworkConfiguration();
            });
        }

        // Privacy toggles
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', () => {
                this.togglePrivacySetting(toggle);
            });
        });

        // Obfuscation intensity selector
        const obfuscationIntensity = document.getElementById('obfuscationIntensity');
        if (obfuscationIntensity) {
            obfuscationIntensity.addEventListener('change', (e) => {
                this.updateObfuscationIntensity(e.target.value);
            });
        }

        // Webview events
        const webview = document.getElementById('webview');
        webview.addEventListener('did-start-loading', () => {
            this.showLoading();
        });

        webview.addEventListener('did-stop-loading', () => {
            this.hideLoading();
        });

        webview.addEventListener('did-navigate', (e) => {
            this.updateAddressBar(e.url);
            this.updatePrivacyIndicator(e.url);
        });

        webview.addEventListener('page-title-updated', (e) => {
            this.updateTabTitle(e.title);
        });
    }

    setupPrivacyPanel() {
        // Initialize privacy panel state
        this.privacyPanelOpen = false;

        // Load privacy settings from main process
        this.loadPrivacySettings();
    }

    setupIpcListeners() {
        // Listen for navigation commands from main process
        if (window.phantomAPI && window.phantomAPI.onNavigateWebview) {
            window.phantomAPI.onNavigateWebview((url) => {
                console.log('Received navigation command for URL:', url);
                const webview = document.getElementById('webview');
                if (webview) {
                    webview.src = url;
                    console.log('Webview src set to:', url);
                } else {
                    console.error('Webview element not found');
                }
            });
        } else {
            // Fallback: use electron's ipcRenderer directly if available
            if (window.require) {
                try {
                    const { ipcRenderer } = window.require('electron');
                    ipcRenderer.on('navigate-webview', (event, url) => {
                        console.log('Received navigation command for URL:', url);
                        const webview = document.getElementById('webview');
                        if (webview) {
                            webview.src = url;
                            console.log('Webview src set to:', url);
                        } else {
                            console.error('Webview element not found');
                        }
                    });
                } catch (error) {
                    console.warn('Could not setup IPC listener:', error);
                }
            }
        }
    }

    initializeSearchEngine() {
        this.searchEngine = new SearchEngine();
        this.setupSearchUI();
        console.log('Search engine initialized');
    }

    setupSearchUI() {
        // Create search suggestions container
        const addressBar = document.getElementById('addressBar');
        if (!addressBar) return;

        // Wrap address bar in container for positioning
        const container = document.createElement('div');
        container.className = 'address-bar-container';
        addressBar.parentNode.insertBefore(container, addressBar);
        container.appendChild(addressBar);

        // Create suggestions dropdown
        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.id = 'searchSuggestions';
        suggestionsContainer.className = 'search-suggestions hidden';
        suggestionsContainer.innerHTML = '<div class="suggestions-list"></div>';
        container.appendChild(suggestionsContainer);

        // Add search provider section to privacy panel
        this.addSearchProviderSection();

        // Setup search event listeners
        this.setupSearchEventListeners();
    }

    addSearchProviderSection() {
        const privacyPanel = document.querySelector('.privacy-panel');
        if (!privacyPanel) return;

        const searchSection = document.createElement('div');
        searchSection.className = 'privacy-section';
        searchSection.innerHTML = `
            <h3>Search Engine</h3>
            <div class="search-provider-selector">
                <label for="searchProvider">Default Search Provider:</label>
                <select id="searchProvider" class="provider-select">
                    ${this.generateProviderOptions()}
                </select>
            </div>
            <div class="search-settings">
                <div class="setting-item">
                    <label class="toggle-switch active" data-setting="searchSuggestions">
                        <span class="toggle-slider"></span>
                    </label>
                    <span class="setting-label">Search Suggestions</span>
                </div>
                <div class="setting-item">
                    <label class="toggle-switch active" data-setting="searchHistory">
                        <span class="toggle-slider"></span>
                    </label>
                    <span class="setting-label">Search History</span>
                </div>
            </div>
            <div class="provider-info">
                <div id="providerDetails" class="provider-details"></div>
            </div>
        `;

        privacyPanel.appendChild(searchSection);
        this.updateProviderInfo();
    }

    generateProviderOptions() {
        const providers = this.searchEngine.getProviders();
        const defaultProvider = this.searchEngine.getDefaultProvider();

        return providers.map(provider =>
            `<option value="${provider.id}" ${provider.id === defaultProvider.id ? 'selected' : ''}>
                ${provider.name} (Privacy: ${provider.privacyRating}/10)
            </option>`
        ).join('');
    }

    setupSearchEventListeners() {
        const addressBar = document.getElementById('addressBar');
        if (!addressBar) return;

        // Remove the basic navigation handler since search engine will handle it
        if (this.addressBarHandler) {
            addressBar.removeEventListener('keypress', this.addressBarHandler);
        }

        let debounceTimer = null;
        let currentSuggestionIndex = -1;
        let suggestions = [];

        // Input event for suggestions
        this.searchInputHandler = async (e) => {
            const value = e.target.value;

            if (debounceTimer) {
                clearTimeout(debounceTimer);
            }

            debounceTimer = setTimeout(async () => {
                try {
                    if (value.length >= 2) {
                        suggestions = await this.searchEngine.getSuggestions(value);
                        this.renderSuggestions(suggestions);
                        this.showSuggestions();
                    } else {
                        this.hideSuggestions();
                    }
                } catch (error) {
                    console.error('Error getting suggestions:', error);
                    this.hideSuggestions();
                }
            }, 300);
        };

        addressBar.addEventListener('input', this.searchInputHandler);

        // Keydown for navigation - replaces the basic keypress handler
        this.searchKeydownHandler = (e) => {
            const suggestionsContainer = document.getElementById('searchSuggestions');
            const hasSuggestions = suggestionsContainer && !suggestionsContainer.classList.contains('hidden');

            switch (e.key) {
                case 'ArrowDown':
                    if (hasSuggestions) {
                        e.preventDefault();
                        currentSuggestionIndex = this.navigateSuggestions(suggestions, currentSuggestionIndex, 1);
                    }
                    break;
                case 'ArrowUp':
                    if (hasSuggestions) {
                        e.preventDefault();
                        currentSuggestionIndex = this.navigateSuggestions(suggestions, currentSuggestionIndex, -1);
                    }
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (hasSuggestions && currentSuggestionIndex >= 0 && suggestions[currentSuggestionIndex]) {
                        this.performSearch(suggestions[currentSuggestionIndex].query);
                    } else {
                        this.performSearch(addressBar.value);
                    }
                    this.hideSuggestions();
                    currentSuggestionIndex = -1;
                    break;
                case 'Escape':
                    if (hasSuggestions) {
                        this.hideSuggestions();
                        currentSuggestionIndex = -1;
                    }
                    break;
            }
        };

        addressBar.addEventListener('keydown', this.searchKeydownHandler);

        // Focus/blur events
        addressBar.addEventListener('focus', () => {
            if (suggestions.length > 0) {
                this.showSuggestions();
            }
        });

        addressBar.addEventListener('blur', () => {
            setTimeout(() => this.hideSuggestions(), 150);
        });

        // Provider selector
        const providerSelect = document.getElementById('searchProvider');
        if (providerSelect) {
            providerSelect.addEventListener('change', (e) => {
                this.searchEngine.setDefaultProvider(e.target.value);
                this.updateProviderInfo();
            });
        }
    }

    async loadInitialSettings() {
        try {
            if (window.phantomAPI) {
                const privacySettings = await window.phantomAPI.getPrivacySettings();
                const userAgentProfile = await window.phantomAPI.getUserAgentProfile();
                const proxySettings = await window.phantomAPI.getProxySettings();
                const steganographicSettings = await window.phantomAPI.getSteganographicSettings();

                this.updateUIFromSettings(privacySettings, userAgentProfile, proxySettings, steganographicSettings);
            }
        } catch (error) {
            console.error('Failed to load initial settings:', error);
        }
    }

    updateUIFromSettings(privacySettings, userAgentProfile, proxySettings, steganographicSettings) {
        // Update toggle switches based on settings
        const toggles = {
            'canvasProtection': privacySettings.randomizeCanvasFingerprint,
            'webglProtection': privacySettings.blockFingerprinting,
            'audioProtection': privacySettings.blockFingerprinting,
            'blockTrackers': privacySettings.blockTrackers,
            'blockAds': privacySettings.blockAds,
            'webrtcProtection': privacySettings.blockWebRTC,
            'userAgentRotation': true, // Assume enabled
            'proxyEnabled': proxySettings?.enabled || false,
            'proxyRotation': false, // Assume disabled initially
            'trafficObfuscation': steganographicSettings?.enableTrafficObfuscation || true,
            'timingRandomization': steganographicSettings?.enableTimingRandomization || true,
            'behaviorMasking': steganographicSettings?.enableBehaviorMasking || true,
            'decoyTraffic': steganographicSettings?.enableDecoyTraffic || true,
            'requestPadding': steganographicSettings?.enableRequestPadding || true,
            'dnsObfuscation': steganographicSettings?.enableDNSObfuscation || true
        };

        Object.entries(toggles).forEach(([setting, enabled]) => {
            const toggle = document.querySelector(`[data-setting="${setting}"]`);
            if (toggle) {
                toggle.classList.toggle('active', enabled);
            }
        });

        // Update obfuscation intensity selector
        const intensitySelector = document.getElementById('obfuscationIntensity');
        if (intensitySelector && steganographicSettings?.obfuscationIntensity) {
            intensitySelector.value = steganographicSettings.obfuscationIntensity;
        }
    }

    navigate(url) {
        if (!url) return;

        // Use search engine to process input
        if (this.searchEngine) {
            const result = this.searchEngine.processInput(url);
            url = result.value;
        } else {
            // Fallback to old logic
            if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('file://')) {
                if (url.includes('.') && !url.includes(' ')) {
                    url = 'https://' + url;
                } else {
                    url = `https://duckduckgo.com/?q=${encodeURIComponent(url)}`;
                }
            }
        }

        const webview = document.getElementById('webview');
        webview.src = url;

        if (window.phantomAPI) {
            window.phantomAPI.navigate(url);
        }
    }

    performSearch(query) {
        if (this.searchEngine) {
            const result = this.searchEngine.processInput(query);
            const addressBar = document.getElementById('addressBar');
            if (addressBar) {
                addressBar.value = result.type === 'url' ? result.value : query;
            }
            this.navigate(result.value);
        } else {
            this.navigate(query);
        }
    }

    renderSuggestions(suggestions) {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (!suggestionsContainer) return;

        const suggestionsList = suggestionsContainer.querySelector('.suggestions-list');
        if (!suggestionsList) return;

        suggestionsList.innerHTML = suggestions.map((suggestion, index) => `
            <div class="suggestion-item" data-index="${index}">
                <div class="suggestion-icon">
                    ${this.getSuggestionIcon(suggestion.type)}
                </div>
                <div class="suggestion-text">
                    <span class="suggestion-query">${this.escapeHtml(suggestion.query)}</span>
                    ${suggestion.title ? `<span class="suggestion-title">${this.escapeHtml(suggestion.title)}</span>` : ''}
                </div>
                <div class="suggestion-type">${suggestion.type}</div>
            </div>
        `).join('');

        // Add click listeners
        suggestionsList.querySelectorAll('.suggestion-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.performSearch(suggestions[index].query);
                this.hideSuggestions();
            });
        });
    }

    getSuggestionIcon(type) {
        switch (type) {
            case 'history':
                return '🕒';
            case 'bookmark':
                return '⭐';
            case 'suggestion':
            default:
                return '🔍';
        }
    }

    navigateSuggestions(suggestions, currentIndex, direction) {
        const suggestionElements = document.querySelectorAll('.suggestion-item');
        if (!suggestionElements || suggestionElements.length === 0) return currentIndex;

        // Remove current highlight
        if (currentIndex >= 0) {
            suggestionElements[currentIndex].classList.remove('highlighted');
        }

        // Calculate new index
        let newIndex = currentIndex + direction;
        if (newIndex < 0) {
            newIndex = suggestionElements.length - 1;
        } else if (newIndex >= suggestionElements.length) {
            newIndex = 0;
        }

        // Highlight new suggestion
        suggestionElements[newIndex].classList.add('highlighted');

        // Update address bar with suggestion
        const addressBar = document.getElementById('addressBar');
        if (addressBar && suggestions[newIndex]) {
            addressBar.value = suggestions[newIndex].query;
        }

        return newIndex;
    }

    showSuggestions() {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (suggestionsContainer) {
            suggestionsContainer.classList.remove('hidden');
        }
    }

    hideSuggestions() {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (suggestionsContainer) {
            suggestionsContainer.classList.add('hidden');
        }
    }

    updateProviderInfo() {
        if (!this.searchEngine) return;

        const provider = this.searchEngine.getDefaultProvider();
        const detailsElement = document.getElementById('providerDetails');

        if (detailsElement) {
            detailsElement.innerHTML = `
                <div class="provider-card">
                    <h4>${provider.name}</h4>
                    <p class="provider-description">${provider.description}</p>
                    <div class="provider-rating">
                        Privacy Rating: <span class="rating-value">${provider.privacyRating}/10</span>
                        <div class="rating-bar">
                            <div class="rating-fill" style="width: ${provider.privacyRating * 10}%"></div>
                        </div>
                    </div>
                    <div class="provider-features">
                        <strong>Features:</strong>
                        <ul>
                            ${provider.features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Cleanup method for search UI
    destroySearchUI() {
        const addressBar = document.getElementById('addressBar');
        if (addressBar) {
            if (this.searchInputHandler) {
                addressBar.removeEventListener('input', this.searchInputHandler);
            }
            if (this.searchKeydownHandler) {
                addressBar.removeEventListener('keydown', this.searchKeydownHandler);
            }
            if (this.addressBarHandler) {
                addressBar.removeEventListener('keypress', this.addressBarHandler);
            }
        }

        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (suggestionsContainer) {
            suggestionsContainer.remove();
        }

        // Clear any pending timers
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
    }

    // Notification system for user feedback
    showNotification(message, type = 'info', duration = 3000) {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.phantom-notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `phantom-notification phantom-notification-${type}`;
        notification.textContent = message;

        // Style the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            transform: translateX(100%);
        `;

        // Set background color based on type
        switch (type) {
            case 'success':
                notification.style.backgroundColor = '#4CAF50';
                break;
            case 'error':
                notification.style.backgroundColor = '#f44336';
                break;
            case 'warning':
                notification.style.backgroundColor = '#ff9800';
                break;
            default:
                notification.style.backgroundColor = '#2196F3';
        }

        // Add to DOM
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);

        // Auto-remove after duration
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, duration);
    }

    createNewTab() {
        const tabId = 'tab-' + Date.now();
        this.tabs.set(tabId, {
            id: tabId,
            title: 'New Tab',
            url: 'about:blank'
        });

        // Create tab element
        const tabBar = document.querySelector('.tab-bar');
        const newTabBtn = document.getElementById('newTabBtn');
        
        const tabElement = document.createElement('div');
        tabElement.className = 'tab';
        tabElement.dataset.tabId = tabId;
        tabElement.innerHTML = `
            <div class="tab-title">New Tab</div>
            <button class="tab-close">×</button>
        `;

        tabBar.insertBefore(tabElement, newTabBtn);

        // Add event listeners
        tabElement.addEventListener('click', (e) => {
            if (!e.target.classList.contains('tab-close')) {
                this.switchTab(tabId);
            }
        });

        tabElement.querySelector('.tab-close').addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeTab(tabId);
        });

        this.switchTab(tabId);
    }

    switchTab(tabId) {
        // Update active tab
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
        if (tabElement) {
            tabElement.classList.add('active');
        }

        this.currentTabId = tabId;
        
        // Update webview content
        const tab = this.tabs.get(tabId);
        if (tab && tab.url !== 'about:blank') {
            const webview = document.getElementById('webview');
            webview.src = tab.url;
        }
    }

    closeTab(tabId) {
        const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
        if (tabElement) {
            tabElement.remove();
        }

        this.tabs.delete(tabId);

        // Switch to another tab if current tab was closed
        if (this.currentTabId === tabId) {
            const remainingTabs = document.querySelectorAll('.tab');
            if (remainingTabs.length > 0) {
                const firstTab = remainingTabs[0];
                this.switchTab(firstTab.dataset.tabId);
            }
        }
    }

    updateTabTitle(title) {
        const tabElement = document.querySelector(`[data-tab-id="${this.currentTabId}"] .tab-title`);
        if (tabElement) {
            tabElement.textContent = title || 'New Tab';
        }

        const tab = this.tabs.get(this.currentTabId);
        if (tab) {
            tab.title = title;
        }
    }

    updateAddressBar(url) {
        const addressBar = document.getElementById('addressBar');
        addressBar.value = url;

        const tab = this.tabs.get(this.currentTabId);
        if (tab) {
            tab.url = url;
        }
    }

    updatePrivacyIndicator(url) {
        const indicator = document.getElementById('privacyIndicator');
        const statusDot = indicator.querySelector('.status-indicator');
        
        if (url.startsWith('https://')) {
            indicator.className = 'privacy-indicator';
            indicator.innerHTML = '<span class="status-indicator active"></span>Protected';
        } else if (url.startsWith('http://')) {
            indicator.className = 'privacy-indicator warning';
            indicator.innerHTML = '<span class="status-indicator warning"></span>Unsecured';
        } else {
            indicator.className = 'privacy-indicator';
            indicator.innerHTML = '<span class="status-indicator active"></span>Local';
        }
    }

    togglePrivacyPanel() {
        const panel = document.getElementById('privacyPanel');
        this.privacyPanelOpen = !this.privacyPanelOpen;
        panel.classList.toggle('open', this.privacyPanelOpen);
    }

    async togglePrivacySetting(toggle) {
        const setting = toggle.dataset.setting;
        const isActive = toggle.classList.contains('active');
        const newState = !isActive;

        // Optimistically update UI
        toggle.classList.toggle('active');

        try {
            if (window.phantomAPI) {
                const currentSettings = await window.phantomAPI.getPrivacySettings();
                
                // Map UI settings to privacy settings
                const settingMap = {
                    'canvasProtection': 'randomizeCanvasFingerprint',
                    'webglProtection': 'blockFingerprinting',
                    'audioProtection': 'blockFingerprinting',
                    'blockTrackers': 'blockTrackers',
                    'blockAds': 'blockAds',
                    'webrtcProtection': 'blockWebRTC'
                };

                if (settingMap[setting]) {
                    currentSettings[settingMap[setting]] = !isActive;
                    await window.phantomAPI.updatePrivacySettings(currentSettings);
                }

                // Handle special settings
                if (setting === 'userAgentRotation') {
                    if (!isActive) {
                        await window.phantomAPI.enableUserAgentRotation();
                    } else {
                        await window.phantomAPI.disableUserAgentRotation();
                    }
                }

                if (setting === 'proxyEnabled') {
                    if (!isActive) {
                        // Enable proxy with default settings
                        await window.phantomAPI.setProxy({
                            type: 'socks5',
                            host: '127.0.0.1',
                            port: 9050,
                            enabled: true
                        });
                    } else {
                        await window.phantomAPI.clearProxy();
                    }
                }

                if (setting === 'proxyRotation') {
                    if (!isActive) {
                        await window.phantomAPI.enableProxyRotation(10);
                    } else {
                        await window.phantomAPI.disableProxyRotation();
                    }
                }

                // Handle steganographic settings
                const steganographicSettings = {
                    'trafficObfuscation': 'enableTrafficObfuscation',
                    'timingRandomization': 'enableTimingRandomization',
                    'behaviorMasking': 'enableBehaviorMasking',
                    'decoyTraffic': 'enableDecoyTraffic',
                    'requestPadding': 'enableRequestPadding',
                    'dnsObfuscation': 'enableDNSObfuscation'
                };

                if (steganographicSettings[setting]) {
                    const currentSteganographicSettings = await window.phantomAPI.getSteganographicSettings();
                    currentSteganographicSettings[steganographicSettings[setting]] = !isActive;
                    await window.phantomAPI.updateSteganographicSettings(currentSteganographicSettings);
                }
            }
        } catch (error) {
            console.error('Failed to update privacy setting:', error);
            // Revert toggle state on error
            toggle.classList.toggle('active');
            // Show error notification to user
            this.showNotification(`Failed to ${newState ? 'enable' : 'disable'} ${setting}`, 'error');
        }
    }

    async rotateUserAgent() {
        try {
            if (window.phantomAPI) {
                await window.phantomAPI.rotateUserAgent();
                this.showNotification('User agent rotated successfully');
            }
        } catch (error) {
            console.error('Failed to rotate user agent:', error);
            this.showNotification('Failed to rotate user agent', 'error');
        }
    }

    async clearBrowsingData() {
        // Show confirmation dialog
        const confirmed = await this.showConfirmationDialog(
            'Clear Browsing Data',
            'This will permanently delete all browsing data including cookies, cache, history, and stored information. This action cannot be undone.',
            'Clear Data',
            'Cancel'
        );

        if (!confirmed) {
            return;
        }

        const button = document.getElementById('clearBrowsingDataBtn');
        const spinner = document.getElementById('clearDataSpinner');
        const buttonText = button.querySelector('.btn-text');

        try {
            // Show loading state
            button.disabled = true;
            spinner.style.display = 'inline';
            buttonText.textContent = 'Clearing...';

            if (window.phantomAPI) {
                const result = await window.phantomAPI.clearBrowsingData();

                if (result.success) {
                    this.showNotification('Browsing data cleared successfully', 'success');
                } else {
                    throw new Error(result.error || 'Unknown error occurred');
                }
            } else {
                throw new Error('Phantom API not available');
            }
        } catch (error) {
            console.error('Failed to clear browsing data:', error);
            this.showNotification(`Failed to clear browsing data: ${error.message}`, 'error');
        } finally {
            // Reset button state
            button.disabled = false;
            spinner.style.display = 'none';
            buttonText.textContent = 'Clear Browsing Data';
        }
    }

    async showConfirmationDialog(title, message, confirmText, cancelText) {
        return new Promise((resolve) => {
            // Create modal overlay
            const overlay = document.createElement('div');
            overlay.className = 'modal-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            // Create modal dialog
            const modal = document.createElement('div');
            modal.className = 'confirmation-modal';
            modal.style.cssText = `
                background: white;
                border-radius: 8px;
                padding: 24px;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            `;

            modal.innerHTML = `
                <h3 style="margin: 0 0 16px 0; color: #333; font-size: 18px;">${title}</h3>
                <p style="margin: 0 0 24px 0; color: #666; line-height: 1.4;">${message}</p>
                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button id="cancelBtn" style="
                        padding: 8px 16px;
                        border: 1px solid #ddd;
                        background: white;
                        color: #333;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                    ">${cancelText}</button>
                    <button id="confirmBtn" style="
                        padding: 8px 16px;
                        border: none;
                        background: #dc3545;
                        color: white;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                    ">${confirmText}</button>
                </div>
            `;

            overlay.appendChild(modal);
            document.body.appendChild(overlay);

            // Add event listeners
            const confirmBtn = modal.querySelector('#confirmBtn');
            const cancelBtn = modal.querySelector('#cancelBtn');

            const cleanup = () => {
                document.body.removeChild(overlay);
            };

            confirmBtn.addEventListener('click', () => {
                cleanup();
                resolve(true);
            });

            cancelBtn.addEventListener('click', () => {
                cleanup();
                resolve(false);
            });

            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    cleanup();
                    resolve(false);
                }
            });
        });
    }

    showLoading() {
        document.getElementById('loadingIndicator').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
    }

    showNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'error' ? '#dc3545' : '#28a745'};
            color: white;
            border-radius: 4px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    startStatsUpdater() {
        // Update privacy stats every 5 seconds
        setInterval(() => {
            this.updatePrivacyStats();
        }, 5000);
    }

    updatePrivacyStats() {
        // Simulate stats updates (in real implementation, get from main process)
        this.privacyStats.blockedTrackers += Math.floor(Math.random() * 3);
        this.privacyStats.blockedAds += Math.floor(Math.random() * 5);
        this.privacyStats.fingerprintAttempts += Math.floor(Math.random() * 2);
        this.privacyStats.dataLeaks += Math.floor(Math.random() * 1);
        this.privacyStats.decoyRequests += Math.floor(Math.random() * 4);
        this.privacyStats.obfuscatedPackets += Math.floor(Math.random() * 8);

        // Update UI
        document.getElementById('blockedTrackers').textContent = this.privacyStats.blockedTrackers;
        document.getElementById('blockedAds').textContent = this.privacyStats.blockedAds;
        document.getElementById('fingerprintAttempts').textContent = this.privacyStats.fingerprintAttempts;
        document.getElementById('dataLeaks').textContent = this.privacyStats.dataLeaks;
        document.getElementById('decoyRequests').textContent = this.privacyStats.decoyRequests;
        document.getElementById('obfuscatedPackets').textContent = this.privacyStats.obfuscatedPackets;
    }

    async updateObfuscationIntensity(intensity) {
        try {
            if (window.phantomAPI) {
                const currentSettings = await window.phantomAPI.getSteganographicSettings();
                currentSettings.obfuscationIntensity = intensity;
                await window.phantomAPI.updateSteganographicSettings(currentSettings);
                this.showNotification(`Obfuscation intensity set to ${intensity}`);
            }
        } catch (error) {
            console.error('Failed to update obfuscation intensity:', error);
            this.showNotification('Failed to update obfuscation intensity', 'error');
        }
    }

    async loadPrivacySettings() {
        try {
            if (window.phantomAPI) {
                const settings = await window.phantomAPI.getPrivacySettings();
                console.log('Privacy settings loaded:', settings);
            }
        } catch (error) {
            console.error('Failed to load privacy settings:', error);
        }
    }

    async refreshNetworkStatus() {
        const button = document.getElementById('refreshNetworkStatusBtn');
        const spinner = document.getElementById('refreshNetworkSpinner');
        const buttonText = button.querySelector('.btn-text');

        try {
            // Show loading state
            button.disabled = true;
            spinner.style.display = 'inline';
            buttonText.textContent = 'Refreshing...';

            if (window.phantomAPI) {
                const networkStatus = await window.phantomAPI.getNetworkStatus();
                this.updateNetworkStatusDisplay(networkStatus);
                this.showNotification('Network status refreshed', 'success');
            } else {
                throw new Error('Phantom API not available');
            }
        } catch (error) {
            console.error('Failed to refresh network status:', error);
            this.showNotification('Failed to refresh network status', 'error');
        } finally {
            // Reset button state
            button.disabled = false;
            spinner.style.display = 'none';
            buttonText.textContent = 'Refresh Status';
        }
    }

    updateNetworkStatusDisplay(networkStatus) {
        const statusCard = document.getElementById('networkStatusCard');
        const statusIcon = document.getElementById('networkStatusIcon');
        const statusMode = document.getElementById('networkStatusMode');
        const statusIndicator = document.getElementById('networkStatusIndicator');
        const statusDetails = document.getElementById('networkStatusDetails');

        if (!networkStatus) {
            // Default to direct connection
            networkStatus = {
                mode: 'direct',
                active: true,
                details: 'Direct internet connection',
                lastUpdated: new Date()
            };
        }

        // Update mode display
        const modeConfig = {
            direct: {
                icon: '🔗',
                name: 'Direct Connection',
                description: 'Direct internet connection - No proxy or DNS filtering',
                className: 'direct'
            },
            proxy: {
                icon: '🌐',
                name: 'Proxy Connection',
                description: networkStatus.details || 'Connected through proxy server',
                className: 'proxy'
            },
            doh: {
                icon: '🔒',
                name: 'DNS over HTTPS',
                description: networkStatus.details || 'Using encrypted DNS resolution',
                className: 'doh'
            }
        };

        const config = modeConfig[networkStatus.mode] || modeConfig.direct;

        // Update UI elements
        statusIcon.textContent = config.icon;
        statusMode.textContent = config.name;
        statusDetails.textContent = config.description;

        // Update status indicator
        statusIndicator.className = 'network-status-indicator';
        if (networkStatus.active) {
            statusIndicator.classList.add('active');
            statusIndicator.setAttribute('data-tooltip', 'Connection active');
        } else {
            statusIndicator.classList.add('inactive');
            statusIndicator.setAttribute('data-tooltip', 'Connection inactive');
        }

        // Update card class
        statusCard.className = `network-status-card ${config.className}`;

        // Add tooltip to the status card
        const lastUpdated = new Date(networkStatus.lastUpdated).toLocaleTimeString();
        statusCard.setAttribute('data-tooltip', `Last updated: ${lastUpdated}`);
    }

    openNetworkConfiguration() {
        // Create a simple network configuration modal
        this.showNetworkConfigurationModal();
    }

    showNetworkConfigurationModal() {
        // Create modal overlay
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        // Create modal dialog
        const modal = document.createElement('div');
        modal.className = 'network-config-modal';
        modal.style.cssText = `
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-height: 80vh;
            overflow-y: auto;
        `;

        modal.innerHTML = `
            <h3 style="margin: 0 0 20px 0; color: #333; font-size: 18px;">Network Configuration</h3>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Connection Mode:</label>
                <select id="networkModeSelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="direct">Direct Connection</option>
                    <option value="proxy">Proxy Server</option>
                    <option value="doh">DNS over HTTPS</option>
                </select>
            </div>

            <div id="proxyConfig" style="display: none; margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Proxy Host:</label>
                <input type="text" id="proxyHost" placeholder="proxy.example.com" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;">

                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Proxy Port:</label>
                <input type="number" id="proxyPort" placeholder="8080" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <div id="dohConfig" style="display: none; margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 600;">DNS Provider:</label>
                <select id="dohProvider" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="https://*******/dns-query">Cloudflare (*******)</option>
                    <option value="https://*******/dns-query">Quad9 (*******)</option>
                    <option value="https://*******/dns-query">Google (*******)</option>
                </select>
            </div>

            <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">
                <button id="cancelNetworkConfig" style="
                    padding: 8px 16px;
                    border: 1px solid #ddd;
                    background: white;
                    color: #333;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                ">Cancel</button>
                <button id="applyNetworkConfig" style="
                    padding: 8px 16px;
                    border: none;
                    background: #007bff;
                    color: white;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 500;
                ">Apply Configuration</button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // Add event listeners
        const modeSelect = modal.querySelector('#networkModeSelect');
        const proxyConfig = modal.querySelector('#proxyConfig');
        const dohConfig = modal.querySelector('#dohConfig');
        const cancelBtn = modal.querySelector('#cancelNetworkConfig');
        const applyBtn = modal.querySelector('#applyNetworkConfig');

        modeSelect.addEventListener('change', () => {
            const mode = modeSelect.value;
            proxyConfig.style.display = mode === 'proxy' ? 'block' : 'none';
            dohConfig.style.display = mode === 'doh' ? 'block' : 'none';
        });

        const cleanup = () => {
            document.body.removeChild(overlay);
        };

        cancelBtn.addEventListener('click', cleanup);

        applyBtn.addEventListener('click', async () => {
            try {
                const mode = modeSelect.value;

                if (window.phantomAPI) {
                    let result;

                    switch (mode) {
                        case 'direct':
                            result = await window.phantomAPI.switchToDirect();
                            break;
                        case 'proxy':
                            const host = modal.querySelector('#proxyHost').value;
                            const port = parseInt(modal.querySelector('#proxyPort').value);
                            if (!host || !port) {
                                throw new Error('Please enter valid proxy host and port');
                            }
                            result = await window.phantomAPI.switchToProxy({
                                type: 'http',
                                host,
                                port,
                                enabled: true
                            });
                            break;
                        case 'doh':
                            const provider = modal.querySelector('#dohProvider').value;
                            result = await window.phantomAPI.switchToDoH([provider]);
                            break;
                    }

                    if (result && result.success) {
                        this.showNotification('Network configuration applied successfully', 'success');
                        await this.refreshNetworkStatus();
                    } else {
                        throw new Error(result?.error || 'Configuration failed');
                    }
                } else {
                    throw new Error('Phantom API not available');
                }

                cleanup();
            } catch (error) {
                console.error('Failed to apply network configuration:', error);
                this.showNotification(`Failed to apply configuration: ${error.message}`, 'error');
            }
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                cleanup();
            }
        });
    }

    // Initialize network status on page load
    async initializeNetworkStatus() {
        try {
            if (window.phantomAPI) {
                const networkStatus = await window.phantomAPI.getNetworkStatus();
                this.updateNetworkStatusDisplay(networkStatus);
            }
        } catch (error) {
            console.error('Failed to initialize network status:', error);
            // Use default status
            this.updateNetworkStatusDisplay(null);
        }
    }
}

// Initialize the browser UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.phantomBrowserUI = new PhantomBrowserUI();
    console.log('Phantom Browser UI initialized');

    // Initialize network status display
    setTimeout(() => {
        if (window.phantomBrowserUI && window.phantomBrowserUI.initializeNetworkStatus) {
            window.phantomBrowserUI.initializeNetworkStatus();
        }
    }, 1000);

    // Add debugging tools to window for console access
    window.phantomDebug = {
        // Test API availability
        testAPI: async () => {
            console.log('=== API Availability Test ===');
            console.log('phantomAPI available:', !!window.phantomAPI);

            if (window.phantomAPI) {
                const methods = [
                    'getPrivacySettings',
                    'updatePrivacySettings',
                    'getSteganographicSettings',
                    'updateSteganographicSettings',
                    'navigate',
                    'enableUserAgentRotation',
                    'disableUserAgentRotation'
                ];

                for (const method of methods) {
                    console.log(`${method}:`, typeof window.phantomAPI[method]);
                }
            }
        },

        // Test privacy settings
        testPrivacySettings: async () => {
            console.log('=== Privacy Settings Test ===');
            try {
                const settings = await window.phantomAPI.getPrivacySettings();
                console.log('Current privacy settings:', settings);

                // Test update
                const testUpdate = { ...settings, blockTrackers: !settings.blockTrackers };
                console.log('Testing update with:', testUpdate);
                const result = await window.phantomAPI.updatePrivacySettings(testUpdate);
                console.log('Update result:', result);

                // Verify update
                const newSettings = await window.phantomAPI.getPrivacySettings();
                console.log('Settings after update:', newSettings);

            } catch (error) {
                console.error('Privacy settings test failed:', error);
            }
        },

        // Test navigation
        testNavigation: async (url = 'https://example.com') => {
            console.log('=== Navigation Test ===');
            try {
                console.log('Testing navigation to:', url);
                const result = await window.phantomAPI.navigate(url);
                console.log('Navigation result:', result);
            } catch (error) {
                console.error('Navigation test failed:', error);
            }
        },

        // Test webview
        testWebview: () => {
            console.log('=== Webview Test ===');
            const webview = document.getElementById('webview');
            console.log('Webview element:', webview);
            console.log('Webview src:', webview?.src);
            console.log('Webview ready state:', webview?.readyState);
        },

        // Test all toggles
        testAllToggles: async () => {
            console.log('=== Toggle Test ===');
            const toggles = document.querySelectorAll('.toggle-switch');
            console.log('Found toggles:', toggles.length);

            for (const toggle of toggles) {
                const setting = toggle.dataset.setting;
                const isActive = toggle.classList.contains('active');
                console.log(`Toggle ${setting}: ${isActive ? 'active' : 'inactive'}`);

                try {
                    console.log(`Testing toggle: ${setting}`);
                    await window.phantomBrowserUI.togglePrivacySetting(toggle);
                    console.log(`Toggle ${setting} completed successfully`);
                } catch (error) {
                    console.error(`Toggle ${setting} failed:`, error);
                }
            }
        },

        // Test search engine
        testSearchEngine: () => {
            console.log('=== Search Engine Test ===');
            const searchEngine = window.phantomBrowserUI.searchEngine;
            if (searchEngine) {
                console.log('Search engine available:', true);
                console.log('Available providers:', searchEngine.getProviders().map(p => p.name));
                console.log('Default provider:', searchEngine.getDefaultProvider().name);
                console.log('Settings:', searchEngine.getSettings());

                // Test search
                const testQuery = 'test search';
                const result = searchEngine.processInput(testQuery);
                console.log(`Search result for "${testQuery}":`, result);

                // Test URL detection
                const testUrl = 'example.com';
                const urlResult = searchEngine.processInput(testUrl);
                console.log(`URL result for "${testUrl}":`, urlResult);
            } else {
                console.error('Search engine not available');
            }
        },

        // Test search suggestions
        testSearchSuggestions: async (query = 'test') => {
            console.log('=== Search Suggestions Test ===');
            const searchEngine = window.phantomBrowserUI.searchEngine;
            if (searchEngine) {
                try {
                    const suggestions = await searchEngine.getSuggestions(query);
                    console.log(`Suggestions for "${query}":`, suggestions);
                } catch (error) {
                    console.error('Failed to get suggestions:', error);
                }
            } else {
                console.error('Search engine not available');
            }
        },

        // Test behavioral obfuscation fix
        testAutoTypingFix: () => {
            console.log('=== Auto-Typing Fix Test ===');
            const addressBar = document.getElementById('addressBar');
            if (addressBar) {
                console.log('Address bar found');
                console.log('Address bar ID:', addressBar.id);
                console.log('Address bar classes:', addressBar.className);
                console.log('Address bar type:', addressBar.type);

                // Focus on address bar to test if auto-typing still occurs
                addressBar.focus();
                console.log('Address bar focused - check if auto-typing occurs');

                setTimeout(() => {
                    console.log('Address bar value after 5 seconds:', addressBar.value);
                }, 5000);
            } else {
                console.error('Address bar not found');
            }
        },

        // Comprehensive system test
        runFullSystemTest: async () => {
            console.log('=== FULL SYSTEM TEST ===');

            // Test 1: API availability
            console.log('1. Testing API availability...');
            phantomDebug.testAPI();

            // Test 2: Search engine
            console.log('2. Testing search engine...');
            phantomDebug.testSearchEngine();

            // Test 3: Privacy toggles
            console.log('3. Testing privacy toggles...');
            await phantomDebug.testAllToggles();

            // Test 4: Navigation
            console.log('4. Testing navigation...');
            phantomDebug.testNavigation('https://duckduckgo.com');

            // Test 5: Auto-typing fix
            console.log('5. Testing auto-typing fix...');
            phantomDebug.testAutoTypingFix();

            // Test 6: Search suggestions
            console.log('6. Testing search suggestions...');
            await phantomDebug.testSearchSuggestions('privacy browser');

            console.log('=== FULL SYSTEM TEST COMPLETE ===');
        },

        // Test error handling
        testErrorHandling: async () => {
            console.log('=== Error Handling Test ===');

            try {
                // Test invalid API call
                const result = await window.phantomAPI.invalidMethod();
                console.log('Unexpected success:', result);
            } catch (error) {
                console.log('✓ Error handling working:', error.message);
            }

            // Test notification system
            if (window.phantomBrowserUI && window.phantomBrowserUI.showNotification) {
                window.phantomBrowserUI.showNotification('Test notification', 'info');
                setTimeout(() => {
                    window.phantomBrowserUI.showNotification('Test error', 'error');
                }, 1000);
                setTimeout(() => {
                    window.phantomBrowserUI.showNotification('Test success', 'success');
                }, 2000);
            }
        },

        // Test performance
        testPerformance: () => {
            console.log('=== Performance Test ===');

            const startTime = performance.now();

            // Test search engine performance
            if (window.phantomBrowserUI && window.phantomBrowserUI.searchEngine) {
                const searchEngine = window.phantomBrowserUI.searchEngine;

                console.time('Search processing');
                for (let i = 0; i < 100; i++) {
                    searchEngine.processInput(`test query ${i}`);
                }
                console.timeEnd('Search processing');

                console.time('URL detection');
                for (let i = 0; i < 100; i++) {
                    searchEngine.isValidUrl(`example${i}.com`);
                }
                console.timeEnd('URL detection');
            }

            const endTime = performance.now();
            console.log(`Total test time: ${endTime - startTime}ms`);

            // Memory usage
            if (performance.memory) {
                console.log('Memory usage:', {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB',
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
                });
            }
        }
    };

    console.log('Debug tools available at window.phantomDebug');
    console.log('Available commands:');
    console.log('- phantomDebug.testAPI()');
    console.log('- phantomDebug.testPrivacySettings()');
    console.log('- phantomDebug.testNavigation("https://example.com")');
    console.log('- phantomDebug.testWebview()');
    console.log('- phantomDebug.testAllToggles()');
    console.log('- phantomDebug.testSearchEngine()');
    console.log('- phantomDebug.testSearchSuggestions("query")');
    console.log('- phantomDebug.testAutoTypingFix()');
    console.log('- phantomDebug.runFullSystemTest() - Comprehensive test');
    console.log('- phantomDebug.testErrorHandling() - Test error handling');
    console.log('- phantomDebug.testPerformance() - Performance benchmarks');
});
