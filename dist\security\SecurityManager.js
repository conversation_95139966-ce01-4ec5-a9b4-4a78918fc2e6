"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityManager = void 0;
const electron_1 = require("electron");
const crypto = __importStar(require("crypto"));
const fs = __importStar(require("fs"));
const SettingsStore_1 = require("../utils/SettingsStore");
class SecurityManager {
    constructor() {
        this.settingsStore = SettingsStore_1.SettingsStore.getInstance();
        this.encryptionKey = crypto.randomBytes(32);
        this.secureStorage = new Map();
        // Load settings from persistent storage
        this.loadSettings();
    }
    async initialize() {
        this.setupSecurityHeaders();
        this.setupDownloadSecurity();
        this.setupContentSecurity();
        this.setupMemoryProtection();
        this.setupProcessIsolation();
    }
    setupSecurityHeaders() {
        const ses = electron_1.session.defaultSession;
        ses.webRequest.onHeadersReceived({ urls: ['<all_urls>'] }, (details, callback) => {
            const responseHeaders = details.responseHeaders || {};
            if (this.settings.enableCSP) {
                responseHeaders['Content-Security-Policy'] = [
                    "default-src 'self'; " +
                        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                        "style-src 'self' 'unsafe-inline'; " +
                        "img-src 'self' data: https:; " +
                        "connect-src 'self' https:; " +
                        "font-src 'self' data:; " +
                        "object-src 'none'; " +
                        "media-src 'self'; " +
                        "frame-src 'none';"
                ];
            }
            if (this.settings.enableHSTS) {
                responseHeaders['Strict-Transport-Security'] = ['max-age=31536000; includeSubDomains'];
            }
            // Security headers
            responseHeaders['X-Frame-Options'] = ['DENY'];
            responseHeaders['X-Content-Type-Options'] = ['nosniff'];
            responseHeaders['X-XSS-Protection'] = ['1; mode=block'];
            responseHeaders['Referrer-Policy'] = ['strict-origin-when-cross-origin'];
            responseHeaders['Permissions-Policy'] = [
                'camera=(), microphone=(), geolocation=(), payment=(), usb=(), ' +
                    'magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), ' +
                    'autoplay=(), encrypted-media=(), fullscreen=(), picture-in-picture=()'
            ];
            // Remove server information
            delete responseHeaders['Server'];
            delete responseHeaders['X-Powered-By'];
            callback({ responseHeaders });
        });
    }
    setupDownloadSecurity() {
        const ses = electron_1.session.defaultSession;
        ses.on('will-download', (event, item, webContents) => {
            if (!this.settings.blockDangerousDownloads) {
                return;
            }
            const filename = item.getFilename();
            const dangerousExtensions = [
                '.exe', '.scr', '.bat', '.cmd', '.com', '.pif', '.vbs', '.js',
                '.jar', '.app', '.deb', '.pkg', '.dmg', '.msi', '.run'
            ];
            const isDangerous = dangerousExtensions.some(ext => filename.toLowerCase().endsWith(ext));
            if (isDangerous) {
                event.preventDefault();
                console.warn(`Blocked dangerous download: ${filename}`);
                return;
            }
            // Scan download for malware (simplified)
            item.on('done', (event, state) => {
                if (state === 'completed') {
                    this.scanFile(item.getSavePath());
                }
            });
        });
    }
    setupContentSecurity() {
        const ses = electron_1.session.defaultSession;
        // Block mixed content
        if (this.settings.blockMixedContent) {
            ses.webRequest.onBeforeRequest({ urls: ['http://*'] }, (details, callback) => {
                const referer = details.referrer;
                if (referer && referer.startsWith('https://')) {
                    console.warn(`Blocked mixed content: ${details.url}`);
                    callback({ cancel: true });
                    return;
                }
                callback({ cancel: false });
            });
        }
        // Block plugins
        if (this.settings.blockPlugins) {
            ses.setPermissionRequestHandler((webContents, permission, callback) => {
                const blockedPermissions = [
                    'plugins', 'flash', 'java', 'silverlight'
                ];
                callback(!blockedPermissions.includes(permission));
            });
        }
        // Block dangerous protocols
        electron_1.app.setAsDefaultProtocolClient = () => false;
        ses.protocol.interceptHttpProtocol('file', (request, callback) => {
            // Block file:// protocol access
            callback({ error: -3 }); // ERR_ABORTED
        });
    }
    setupMemoryProtection() {
        if (!this.settings.enableMemoryProtection) {
            return;
        }
        // Enable memory protection features
        electron_1.app.commandLine.appendSwitch('--enable-features', 'VizDisplayCompositor');
        electron_1.app.commandLine.appendSwitch('--disable-features', 'VizServiceDisplayCompositor');
        electron_1.app.commandLine.appendSwitch('--enable-heap-profiling');
        electron_1.app.commandLine.appendSwitch('--max-old-space-size', '512');
        // Monitor memory usage
        setInterval(() => {
            const memoryUsage = process.memoryUsage();
            if (memoryUsage.heapUsed > 500 * 1024 * 1024) { // 500MB
                console.warn('High memory usage detected, triggering garbage collection');
                if (global.gc) {
                    global.gc();
                }
            }
        }, 30000);
    }
    setupProcessIsolation() {
        // Enable site isolation
        electron_1.app.commandLine.appendSwitch('--site-per-process');
        electron_1.app.commandLine.appendSwitch('--enable-features', 'SitePerProcess');
        // Enable out-of-process iframes
        electron_1.app.commandLine.appendSwitch('--enable-features', 'OutOfProcessIframes');
        // Disable shared array buffer (Spectre mitigation)
        electron_1.app.commandLine.appendSwitch('--disable-features', 'SharedArrayBuffer');
        // Enable strict site isolation
        electron_1.app.commandLine.appendSwitch('--enable-strict-mixed-content-checking');
    }
    async scanFile(filePath) {
        try {
            // Simple file scanning (in production, integrate with antivirus)
            const stats = fs.statSync(filePath);
            // Check file size (block extremely large files)
            if (stats.size > 100 * 1024 * 1024) { // 100MB
                console.warn(`Large file detected: ${filePath}`);
                return false;
            }
            // Check file signature
            const buffer = fs.readFileSync(filePath).slice(0, 10);
            const signature = buffer.toString('hex');
            // Known malicious signatures (simplified)
            const maliciousSignatures = [
                '4d5a', // PE executable
                '7f454c46', // ELF executable
            ];
            const isMalicious = maliciousSignatures.some(sig => signature.startsWith(sig));
            if (isMalicious) {
                console.warn(`Potentially malicious file: ${filePath}`);
                fs.unlinkSync(filePath); // Delete the file
                return false;
            }
            return true;
        }
        catch (error) {
            console.error('File scan error:', error);
            return false;
        }
    }
    // Secure storage methods
    encryptData(data) {
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return iv.toString('hex') + ':' + encrypted;
    }
    decryptData(encryptedData) {
        const parts = encryptedData.split(':');
        const iv = Buffer.from(parts[0], 'hex');
        const encrypted = parts[1];
        const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
    secureStore(key, value) {
        const encrypted = this.encryptData(value);
        this.secureStorage.set(key, encrypted);
    }
    secureRetrieve(key) {
        const encrypted = this.secureStorage.get(key);
        if (!encrypted) {
            return null;
        }
        try {
            return this.decryptData(encrypted);
        }
        catch {
            return null;
        }
    }
    secureDelete(key) {
        this.secureStorage.delete(key);
    }
    // Security audit methods
    async performSecurityAudit() {
        const result = {
            timestamp: new Date(),
            issues: [],
            recommendations: []
        };
        // Check for security issues
        const windows = electron_1.BrowserWindow.getAllWindows();
        for (const window of windows) {
            const webContents = window.webContents;
            if (!webContents.isDevToolsOpened()) {
                result.recommendations.push('Consider enabling dev tools for debugging');
            }
            if (webContents.getURL().startsWith('http://')) {
                result.issues.push('Insecure HTTP connection detected');
            }
        }
        // Check session security
        const ses = electron_1.session.defaultSession;
        const cookies = await ses.cookies.get({});
        for (const cookie of cookies) {
            if (!cookie.secure && cookie.domain !== 'localhost') {
                result.issues.push(`Insecure cookie: ${cookie.name}`);
            }
        }
        return result;
    }
    async clearSecurityData() {
        if (this.settings.clearDataOnExit) {
            const ses = electron_1.session.defaultSession;
            await ses.clearStorageData();
            await ses.clearCache();
            await ses.clearAuthCache();
            this.secureStorage.clear();
            console.log('Security data cleared');
        }
    }
    loadSettings() {
        try {
            this.settings = this.settingsStore.get('security');
            console.log('Security settings loaded from storage');
        }
        catch (error) {
            console.error('Failed to load security settings:', error);
            // Use default settings if loading fails
            this.settings = {
                enableSandbox: true,
                blockDangerousDownloads: true,
                enableCSP: true,
                blockMixedContent: true,
                enableHSTS: true,
                blockPlugins: true,
                enableMemoryProtection: true,
                clearDataOnExit: true
            };
        }
    }
    saveSettings() {
        try {
            this.settingsStore.updateSecuritySettings(this.settings);
            console.log('Security settings saved to storage');
        }
        catch (error) {
            console.error('Failed to save security settings:', error);
            throw new Error(`Failed to save security settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    updateSettings(newSettings) {
        try {
            this.settings = { ...this.settings, ...newSettings };
            this.saveSettings();
        }
        catch (error) {
            console.error('Failed to update security settings:', error);
            throw error;
        }
    }
    getSettings() {
        return { ...this.settings };
    }
}
exports.SecurityManager = SecurityManager;
//# sourceMappingURL=SecurityManager.js.map