import { session } from 'electron';
import { ProxyManager, ProxyConfig } from './ProxyManager';
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON>, <PERSON>rrorCode, ApiResponse } from '../utils/ErrorHandler';
import { SettingsStore } from '../utils/SettingsStore';
import { secureLogger } from '../utils/SecureLogger';

export interface NetworkConfiguration {
    priority: 'proxy' | 'doh' | 'direct';
    proxyConfig?: ProxyConfig;
    dohConfig?: {
        enabled: boolean;
        providers: string[];
        fallbackToDirect: boolean;
    };
    status: 'active' | 'inactive' | 'error';
    lastUpdated: number;
}

export interface DoHProvider {
    name: string;
    url: string;
    description: string;
    privacyRating: number;
}

export class NetworkConfigManager {
    private currentConfig: NetworkConfiguration;
    private proxyManager: ProxyManager;
    private errorHandler: ErrorHandler;
    private settingsStore: SettingsStore;
    private dohProviders: DoHProvider[];

    constructor(proxyManager: ProxyManager) {
        this.proxyManager = proxyManager;
        this.errorHandler = ErrorHandler.getInstance();
        this.settingsStore = SettingsStore.getInstance();
        
        this.dohProviders = [
            {
                name: 'Cloudflare',
                url: 'https://*******/dns-query',
                description: 'Fast and privacy-focused DNS service',
                privacyRating: 9
            },
            {
                name: 'Quad9',
                url: 'https://*******/dns-query',
                description: 'Security-focused DNS with malware blocking',
                privacyRating: 8
            },
            {
                name: 'Google',
                url: 'https://*******/dns-query',
                description: 'Google Public DNS service',
                privacyRating: 6
            },
            {
                name: 'OpenDNS',
                url: 'https://**************/dns-query',
                description: 'Cisco OpenDNS service',
                privacyRating: 7
            }
        ];

        this.currentConfig = {
            priority: 'direct',
            status: 'inactive',
            lastUpdated: Date.now()
        };

        this.loadConfiguration();
    }

    private loadConfiguration(): void {
        try {
            // Load network configuration from settings
            const networkSettings = this.settingsStore.get('proxy');
            
            if (networkSettings.currentProxy) {
                this.currentConfig = {
                    priority: 'proxy',
                    proxyConfig: networkSettings.currentProxy,
                    status: 'active',
                    lastUpdated: Date.now()
                };
            } else {
                // Check if DoH is enabled in privacy settings
                const privacySettings = this.settingsStore.get('privacy');
                if (privacySettings.useDoH) {
                    this.currentConfig = {
                        priority: 'doh',
                        dohConfig: {
                            enabled: true,
                            providers: ['https://*******/dns-query'],
                            fallbackToDirect: true
                        },
                        status: 'active',
                        lastUpdated: Date.now()
                    };
                }
            }

            secureLogger.info('Network configuration loaded successfully', 'NetworkConfigManager');
        } catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.loadConfiguration');
            // Use default direct configuration
            this.currentConfig = {
                priority: 'direct',
                status: 'active',
                lastUpdated: Date.now()
            };
        }
    }

    private saveConfiguration(): ApiResponse<void> {
        try {
            // Save the current configuration state
            // This is handled by the individual managers (ProxyManager, PrivacyManager)
            secureLogger.info('Network configuration state updated', 'NetworkConfigManager');
            return this.errorHandler.createSuccessResponse(undefined, 'Network configuration saved');
        } catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.saveConfiguration');
            return this.errorHandler.createErrorResponse(error, ErrorCode.STORAGE_WRITE_FAILED);
        }
    }

    async setNetworkConfiguration(config: Partial<NetworkConfiguration>): Promise<ApiResponse<void>> {
        try {
            const newConfig: NetworkConfiguration = {
                ...this.currentConfig,
                ...config,
                lastUpdated: Date.now()
            };

            // Validate the configuration
            const validationResult = this.validateConfiguration(newConfig);
            if (!validationResult.success) {
                return validationResult;
            }

            // Apply the configuration based on priority
            const applyResult = await this.applyConfiguration(newConfig);
            if (!applyResult.success) {
                return applyResult;
            }

            this.currentConfig = newConfig;
            this.saveConfiguration();

            return this.errorHandler.createSuccessResponse(
                undefined, 
                `Network configuration updated: ${newConfig.priority} mode active`
            );
        } catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.setNetworkConfiguration', { config });
            return this.errorHandler.createErrorResponse(error, ErrorCode.NETWORK_CONNECTION_FAILED);
        }
    }

    private validateConfiguration(config: NetworkConfiguration): ApiResponse<void> {
        try {
            // Validate priority
            this.errorHandler.validateInput(
                ['proxy', 'doh', 'direct'].includes(config.priority),
                'Invalid network priority. Must be proxy, doh, or direct'
            );

            // Validate proxy configuration if priority is proxy
            if (config.priority === 'proxy') {
                this.errorHandler.validateInput(
                    !!config.proxyConfig,
                    'Proxy configuration is required when priority is proxy'
                );

                if (config.proxyConfig) {
                    const proxyValidation = this.proxyManager.validateProxyConfig(config.proxyConfig);
                    if (!proxyValidation.success) {
                        return proxyValidation;
                    }
                }
            }

            // Validate DoH configuration if priority is doh
            if (config.priority === 'doh') {
                this.errorHandler.validateInput(
                    !!config.dohConfig && config.dohConfig.enabled,
                    'DoH configuration is required when priority is doh'
                );

                if (config.dohConfig) {
                    this.errorHandler.validateInput(
                        config.dohConfig.providers.length > 0,
                        'At least one DoH provider is required'
                    );
                }
            }

            return this.errorHandler.createSuccessResponse(undefined, 'Configuration is valid');
        } catch (error) {
            return this.errorHandler.createErrorResponse(error, ErrorCode.INVALID_INPUT);
        }
    }

    private async applyConfiguration(config: NetworkConfiguration): Promise<ApiResponse<void>> {
        try {
            const ses = session.defaultSession;

            // Clear any existing configuration first
            await this.clearAllNetworkConfiguration();

            switch (config.priority) {
                case 'proxy':
                    if (config.proxyConfig) {
                        await this.proxyManager.setProxy(config.proxyConfig);
                        secureLogger.info('Proxy configuration applied', 'NetworkConfigManager');
                    }
                    break;

                case 'doh':
                    if (config.dohConfig) {
                        await this.setupDoH(config.dohConfig);
                        secureLogger.info('DoH configuration applied', 'NetworkConfigManager');
                    }
                    break;

                case 'direct':
                    // Direct connection - no additional configuration needed
                    secureLogger.info('Direct connection configured', 'NetworkConfigManager');
                    break;

                default:
                    throw new Error(`Unknown network priority: ${config.priority}`);
            }

            return this.errorHandler.createSuccessResponse(undefined, 'Configuration applied successfully');
        } catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.applyConfiguration', { config });
            return this.errorHandler.createErrorResponse(error, ErrorCode.NETWORK_CONNECTION_FAILED);
        }
    }

    private async clearAllNetworkConfiguration(): Promise<void> {
        try {
            const ses = session.defaultSession;
            
            // Clear proxy settings
            await ses.setProxy({ proxyRules: 'direct://' });
            
            // Clear DoH settings (reset to system default)
            await ses.setProxy({ mode: 'system' });
            
            secureLogger.info('All network configurations cleared', 'NetworkConfigManager');
        } catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.clearAllNetworkConfiguration');
            throw error;
        }
    }

    private async setupDoH(dohConfig: { providers: string[]; fallbackToDirect: boolean }): Promise<void> {
        try {
            const ses = session.defaultSession;
            
            // Configure DNS over HTTPS using PAC script
            const pacScript = this.generateDoHPacScript(dohConfig.providers, dohConfig.fallbackToDirect);
            
            await ses.setProxy({
                mode: 'pac_script',
                pacScript
            });

            secureLogger.info(`DoH configured with ${dohConfig.providers.length} providers`, 'NetworkConfigManager');
        } catch (error) {
            this.errorHandler.logError(error, 'NetworkConfigManager.setupDoH', { dohConfig });
            throw error;
        }
    }

    private generateDoHPacScript(providers: string[], fallbackToDirect: boolean): string {
        const providerList = providers.map(provider => `"HTTPS ${provider}"`).join('; ');
        const fallback = fallbackToDirect ? '; DIRECT' : '';
        
        return `
            function FindProxyForURL(url, host) {
                // Use DNS over HTTPS for all requests
                return "${providerList}${fallback}";
            }
        `;
    }

    getCurrentConfiguration(): NetworkConfiguration {
        return { ...this.currentConfig };
    }

    getDoHProviders(): DoHProvider[] {
        return [...this.dohProviders];
    }

    async switchToProxy(proxyConfig: ProxyConfig): Promise<ApiResponse<void>> {
        return this.setNetworkConfiguration({
            priority: 'proxy',
            proxyConfig,
            status: 'active'
        });
    }

    async switchToDoH(providers?: string[]): Promise<ApiResponse<void>> {
        const dohProviders = providers || ['https://*******/dns-query'];
        return this.setNetworkConfiguration({
            priority: 'doh',
            dohConfig: {
                enabled: true,
                providers: dohProviders,
                fallbackToDirect: true
            },
            status: 'active'
        });
    }

    async switchToDirect(): Promise<ApiResponse<void>> {
        return this.setNetworkConfiguration({
            priority: 'direct',
            status: 'active'
        });
    }

    getNetworkStatus(): { 
        mode: string; 
        active: boolean; 
        details: string;
        lastUpdated: Date;
    } {
        const config = this.currentConfig;
        let details = '';

        switch (config.priority) {
            case 'proxy':
                details = config.proxyConfig 
                    ? `${config.proxyConfig.type.toUpperCase()} ${config.proxyConfig.host}:${config.proxyConfig.port}`
                    : 'Proxy configuration missing';
                break;
            case 'doh':
                details = config.dohConfig 
                    ? `DoH providers: ${config.dohConfig.providers.length}`
                    : 'DoH configuration missing';
                break;
            case 'direct':
                details = 'Direct internet connection';
                break;
        }

        return {
            mode: config.priority,
            active: config.status === 'active',
            details,
            lastUpdated: new Date(config.lastUpdated)
        };
    }

    destroy(): void {
        // Cleanup method
        this.clearAllNetworkConfiguration().catch(error => {
            this.errorHandler.logError(error, 'NetworkConfigManager.destroy');
        });
    }
}

export default NetworkConfigManager;
