export interface SearchProvider {
    id: string;
    name: string;
    baseUrl: string;
    searchUrl: string;
    suggestionsUrl?: string;
    icon?: string;
    privacyRating: number;
    features: string[];
    description: string;
}
export interface SearchSuggestion {
    query: string;
    type: 'suggestion' | 'history' | 'bookmark';
    url?: string;
    title?: string;
}
export interface SearchSettings {
    defaultProvider: string;
    enableSuggestions: boolean;
    enableHistory: boolean;
    maxSuggestions: number;
    privacyMode: 'strict' | 'balanced' | 'standard';
    customProviders: SearchProvider[];
}
export declare class SearchEngine {
    private providers;
    private settings;
    private searchHistory;
    private maxHistorySize;
    private settingsStore;
    constructor();
    private initializeDefaultProviders;
    private getDefaultSettings;
    private loadSettings;
    getProviders(): SearchProvider[];
    getProvider(id: string): SearchProvider | undefined;
    getDefaultProvider(): SearchProvider;
    setDefaultProvider(providerId: string): void;
    search(query: string, providerId?: string): string;
    getSuggestions(query: string, providerId?: string): Promise<SearchSuggestion[]>;
    private fetchProviderSuggestions;
    private getHistorySuggestions;
    private addToHistory;
    clearHistory(): void;
    getSettings(): SearchSettings;
    updateSettings(newSettings: Partial<SearchSettings>): void;
    private saveSettings;
    addCustomProvider(provider: SearchProvider): void;
    removeCustomProvider(providerId: string): void;
    getSearchHistory(): Array<{
        query: string;
        timestamp: number;
        provider: string;
    }>;
    isValidUrl(input: string): boolean;
    processInput(input: string): {
        type: 'url' | 'search';
        value: string;
    };
}
//# sourceMappingURL=SearchEngine.d.ts.map