/**
 * Secure Logging Utility for Phantom Browser
 * Sanitizes log messages to prevent sensitive information exposure
 */

export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}

export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: number;
    context?: string;
    sanitized: boolean;
}

export class SecureLogger {
    private static instance: SecureLogger;
    private logLevel: LogLevel = LogLevel.INFO;
    private logHistory: LogEntry[] = [];
    private maxHistorySize: number = 1000;

    private constructor() {}

    public static getInstance(): SecureLogger {
        if (!SecureLogger.instance) {
            SecureLogger.instance = new SecureLogger();
        }
        return SecureLogger.instance;
    }

    /**
     * Set the minimum log level
     */
    public setLogLevel(level: LogLevel): void {
        this.logLevel = level;
    }

    /**
     * Log a debug message
     */
    public debug(message: string, context?: string): void {
        this.log(LogLevel.DEBUG, message, context);
    }

    /**
     * Log an info message
     */
    public info(message: string, context?: string): void {
        this.log(LogLevel.INFO, message, context);
    }

    /**
     * Log a warning message
     */
    public warn(message: string, context?: string): void {
        this.log(LogLevel.WARN, message, context);
    }

    /**
     * Log an error message
     */
    public error(message: string, context?: string): void {
        this.log(LogLevel.ERROR, message, context);
    }

    /**
     * Log a message with automatic sanitization
     */
    private log(level: LogLevel, message: string, context?: string): void {
        if (level < this.logLevel) {
            return;
        }

        const sanitizedMessage = this.sanitizeMessage(message);
        const logEntry: LogEntry = {
            level,
            message: sanitizedMessage,
            timestamp: Date.now(),
            context,
            sanitized: sanitizedMessage !== message
        };

        // Add to history
        this.addToHistory(logEntry);

        // Output to console
        this.outputToConsole(logEntry);
    }

    /**
     * Sanitize log messages to remove sensitive information
     */
    private sanitizeMessage(message: string): string {
        let sanitized = message;

        // Remove passwords
        sanitized = sanitized.replace(/password[=:]\s*[^\s,}]+/gi, 'password=***');
        sanitized = sanitized.replace(/pass[=:]\s*[^\s,}]+/gi, 'pass=***');
        sanitized = sanitized.replace(/"password"\s*:\s*"[^"]+"/gi, '"password":"***"');

        // Remove usernames in credentials context
        sanitized = sanitized.replace(/username[=:]\s*[^\s,}]+/gi, 'username=***');
        sanitized = sanitized.replace(/user[=:]\s*[^\s,}]+/gi, 'user=***');
        sanitized = sanitized.replace(/"username"\s*:\s*"[^"]+"/gi, '"username":"***"');

        // Remove API keys and tokens
        sanitized = sanitized.replace(/api[_-]?key[=:]\s*[^\s,}]+/gi, 'api_key=***');
        sanitized = sanitized.replace(/token[=:]\s*[^\s,}]+/gi, 'token=***');
        sanitized = sanitized.replace(/secret[=:]\s*[^\s,}]+/gi, 'secret=***');

        // Remove file paths (Windows and Unix)
        sanitized = sanitized.replace(/[A-Z]:\\[^\\]+\\[^\\]+\\/gi, '[PATH]\\');
        sanitized = sanitized.replace(/\/[^\/]+\/[^\/]+\//gi, '/[PATH]/');
        sanitized = sanitized.replace(/C:\\Users\\<USER>\\]+/gi, 'C:\\Users\\<USER>\b(?:\d{1,3}\.){3}\d{1,3}:\d+/g, '[IP]:[PORT]');

        // Remove proxy authentication URLs
        sanitized = sanitized.replace(/\/\/[^:]+:[^@]+@/g, '//[USER]:[PASS]@');

        // Remove stack trace information
        sanitized = sanitized.replace(/at .+:\d+:\d+/g, 'at [LOCATION]');

        // Remove system version information
        sanitized = sanitized.replace(/Node\.js v[\d.]+/g, 'Node.js [VERSION]');
        sanitized = sanitized.replace(/Windows \d+[^,\s]*/g, 'Windows [VERSION]');
        sanitized = sanitized.replace(/Electron v[\d.]+/g, 'Electron [VERSION]');

        // Remove detailed error codes that might expose internal structure
        sanitized = sanitized.replace(/Error code: [A-Z0-9_]+/gi, 'Error code: [CODE]');

        return sanitized;
    }

    /**
     * Add log entry to history with size management
     */
    private addToHistory(entry: LogEntry): void {
        this.logHistory.push(entry);
        
        // Maintain history size limit
        if (this.logHistory.length > this.maxHistorySize) {
            this.logHistory = this.logHistory.slice(-this.maxHistorySize);
        }
    }

    /**
     * Output log entry to console with appropriate formatting
     */
    private outputToConsole(entry: LogEntry): void {
        const timestamp = new Date(entry.timestamp).toISOString();
        const levelName = LogLevel[entry.level];
        const contextStr = entry.context ? ` [${entry.context}]` : '';
        const sanitizedIndicator = entry.sanitized ? ' 🔒' : '';
        
        const formattedMessage = `[${timestamp}] ${levelName}${contextStr}: ${entry.message}${sanitizedIndicator}`;

        switch (entry.level) {
            case LogLevel.DEBUG:
                console.debug(formattedMessage);
                break;
            case LogLevel.INFO:
                console.info(formattedMessage);
                break;
            case LogLevel.WARN:
                console.warn(formattedMessage);
                break;
            case LogLevel.ERROR:
                console.error(formattedMessage);
                break;
        }
    }

    /**
     * Get sanitized log history
     */
    public getLogHistory(maxEntries?: number): LogEntry[] {
        const entries = maxEntries ? this.logHistory.slice(-maxEntries) : this.logHistory;
        return entries.map(entry => ({ ...entry })); // Return copies
    }

    /**
     * Clear log history
     */
    public clearHistory(): void {
        this.logHistory = [];
    }

    /**
     * Export logs for debugging (with additional sanitization)
     */
    public exportLogs(): string {
        return this.logHistory
            .map(entry => {
                const timestamp = new Date(entry.timestamp).toISOString();
                const levelName = LogLevel[entry.level];
                const contextStr = entry.context ? ` [${entry.context}]` : '';
                return `[${timestamp}] ${levelName}${contextStr}: ${entry.message}`;
            })
            .join('\n');
    }

    /**
     * Create a scoped logger for a specific context
     */
    public createScopedLogger(context: string): ScopedLogger {
        return new ScopedLogger(this, context);
    }

    /**
     * Test the sanitization with various sensitive data patterns
     */
    public testSanitization(): void {
        const testCases = [
            'User logged in with password=secret123',
            'Proxy config: {host: "proxy.com", username: "user", password: "pass123"}',
            'Failed to connect to user:<EMAIL>:8080',
            'Error at C:\\Users\\<USER>\\AppData\\Local\\phantom\\settings.json:45:12',
            'API key: abc123def456ghi789',
            'Connection failed with token=bearer_xyz789',
            'Stack trace: at Object.<anonymous> (/app/src/privacy/PrivacyManager.js:123:45)'
        ];

        console.log('🧪 Testing SecureLogger sanitization:');
        testCases.forEach((testCase, index) => {
            const sanitized = this.sanitizeMessage(testCase);
            console.log(`Test ${index + 1}:`);
            console.log(`  Original: ${testCase}`);
            console.log(`  Sanitized: ${sanitized}`);
            console.log(`  Safe: ${!this.containsSensitiveData(sanitized)}`);
            console.log('');
        });
    }

    /**
     * Check if a message contains potentially sensitive data
     */
    private containsSensitiveData(message: string): boolean {
        const sensitivePatterns = [
            /password[=:]\s*[^\s,}*]+/i,
            /pass[=:]\s*[^\s,}*]+/i,
            /username[=:]\s*[^\s,}*]+/i,
            /user[=:]\s*[^\s,}*]+/i,
            /api[_-]?key[=:]\s*[^\s,}*]+/i,
            /token[=:]\s*[^\s,}*]+/i,
            /secret[=:]\s*[^\s,}*]+/i,
            /[A-Z]:\\Users\\[^\\]+/,
            /\/home\/<USER>\/]+/,
            /\/\/[^:]+:[^@]+@/
        ];

        return sensitivePatterns.some(pattern => pattern.test(message));
    }
}

/**
 * Scoped logger for specific contexts
 */
export class ScopedLogger {
    constructor(
        private logger: SecureLogger,
        private context: string
    ) {}

    public debug(message: string): void {
        this.logger.debug(message, this.context);
    }

    public info(message: string): void {
        this.logger.info(message, this.context);
    }

    public warn(message: string): void {
        this.logger.warn(message, this.context);
    }

    public error(message: string): void {
        this.logger.error(message, this.context);
    }
}

// Export singleton instance for convenience
export const secureLogger = SecureLogger.getInstance();

export default SecureLogger;
