<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phantom Browser</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            color: #333;
            overflow: hidden;
        }

        .browser-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .title-bar {
            height: 30px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            -webkit-app-region: drag;
            font-size: 12px;
            color: #666;
        }

        .toolbar {
            height: 50px;
            background: #ffffff;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            padding: 0 10px;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 5px;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 4px;
            background: #f8f9fa;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .nav-btn:hover {
            background: #e9ecef;
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .address-bar {
            flex: 1;
            height: 36px;
            border: 1px solid #ddd;
            border-radius: 18px;
            padding: 0 15px;
            font-size: 14px;
            outline: none;
            background: #f8f9fa;
        }

        .address-bar:focus {
            border-color: #007bff;
            background: #fff;
        }

        .privacy-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            background: #28a745;
            color: white;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .privacy-indicator.warning {
            background: #ffc107;
            color: #000;
        }

        .privacy-indicator.danger {
            background: #dc3545;
        }

        .tab-bar {
            height: 40px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            overflow-x: auto;
        }

        .tab {
            min-width: 200px;
            max-width: 250px;
            height: 32px;
            background: #fff;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 8px 8px 0 0;
            margin-right: 2px;
            display: flex;
            align-items: center;
            padding: 0 12px;
            cursor: pointer;
            position: relative;
        }

        .tab.active {
            background: #ffffff;
            border-color: #007bff;
        }

        .tab-title {
            flex: 1;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .tab-close {
            width: 16px;
            height: 16px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
        }

        .tab-close:hover {
            background: #f0f0f0;
        }

        .new-tab-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: #f8f9fa;
            cursor: pointer;
            border-radius: 4px;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .new-tab-btn:hover {
            background: #e9ecef;
        }

        .content-area {
            flex: 1;
            position: relative;
            background: #fff;
        }

        .webview {
            width: 100%;
            height: 100%;
            border: none;
        }

        .privacy-panel {
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 100%;
            background: #ffffff;
            border-left: 1px solid #e0e0e0;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .privacy-panel.open {
            transform: translateX(0);
        }

        .privacy-panel-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #f8f9fa;
        }

        .privacy-panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .privacy-panel-subtitle {
            font-size: 12px;
            color: #666;
        }

        .privacy-section {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .privacy-section-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .privacy-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .toggle-switch.active {
            background: #007bff;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.active {
            background: #28a745;
        }

        .status-indicator.inactive {
            background: #dc3545;
        }

        .status-indicator.warning {
            background: #ffc107;
        }

        .privacy-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .stat-number {
            font-size: 18px;
            font-weight: 600;
            color: #007bff;
        }

        .stat-label {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
        }

        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }

        /* Privacy Action Buttons */
        .privacy-action-buttons {
            margin-top: 10px;
        }

        .privacy-action-btn {
            width: 100%;
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            background: #007bff;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.2s ease;
            margin-bottom: 8px;
        }

        .privacy-action-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .privacy-action-btn.danger {
            background: #dc3545;
        }

        .privacy-action-btn.danger:hover {
            background: #c82333;
        }

        .privacy-action-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .btn-icon {
            font-size: 16px;
        }

        .btn-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .action-description {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 5px;
            line-height: 1.3;
        }

        /* Search Engine Styles */
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }

        .search-suggestions.hidden {
            display: none;
        }

        .suggestions-list {
            padding: 0;
            margin: 0;
        }

        .suggestion-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }

        .suggestion-item:hover,
        .suggestion-item.highlighted {
            background-color: #f5f5f5;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-icon {
            margin-right: 12px;
            font-size: 16px;
            opacity: 0.7;
        }

        .suggestion-text {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .suggestion-query {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .suggestion-title {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .suggestion-type {
            font-size: 11px;
            color: #999;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            opacity: 0.6;
        }

        /* Search Provider Selector */
        .search-provider-selector {
            margin-bottom: 16px;
        }

        .search-provider-selector label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .provider-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            color: #333;
            font-size: 14px;
        }

        .provider-select:focus {
            outline: none;
            border-color: #4a90e2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }

        /* Search Settings */
        .search-settings {
            margin-bottom: 20px;
        }

        .search-settings .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .search-settings .setting-label {
            font-size: 14px;
            color: #333;
        }

        /* Provider Info */
        .provider-info {
            margin-top: 20px;
        }

        .provider-card {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
        }

        .provider-card h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 16px;
        }

        .provider-description {
            margin: 0 0 12px 0;
            color: #666;
            font-size: 13px;
            line-height: 1.4;
        }

        .provider-rating {
            margin-bottom: 16px;
        }

        .rating-value {
            font-weight: 600;
            color: #4a90e2;
        }

        .rating-bar {
            width: 100%;
            height: 6px;
            background: #eee;
            border-radius: 3px;
            margin-top: 4px;
            overflow: hidden;
        }

        .rating-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4444 0%, #ffaa00 50%, #00aa44 100%);
            transition: width 0.3s ease;
        }

        .provider-features {
            font-size: 13px;
        }

        .provider-features strong {
            color: #333;
            display: block;
            margin-bottom: 8px;
        }

        .provider-features ul {
            margin: 0;
            padding-left: 16px;
            color: #666;
        }

        .provider-features li {
            margin-bottom: 4px;
        }

        /* Address bar container positioning */
        .address-bar-container {
            position: relative;
            flex: 1;
            max-width: 600px;
        }

        /* Ensure navigation bar can accommodate search suggestions */
        .navigation-bar {
            position: relative;
            z-index: 1000;
        }

        /* Improve search suggestions z-index and positioning */
        .search-suggestions {
            z-index: 1001;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
            border: 1px solid #ccc;
        }

        /* Responsive design improvements */
        @media (max-width: 1024px) {
            .address-bar-container {
                max-width: 400px;
            }

            .search-suggestions {
                max-height: 250px;
            }
        }

        @media (max-width: 768px) {
            .address-bar-container {
                max-width: 300px;
            }

            .search-suggestions {
                max-height: 200px;
            }

            .suggestion-item {
                padding: 10px 12px;
            }

            .provider-card {
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="browser-container">
        <div class="title-bar">
            Phantom Browser - Advanced Privacy Protection
        </div>

        <div class="tab-bar">
            <div class="tab active" data-tab-id="default">
                <div class="tab-title">New Tab</div>
                <button class="tab-close">×</button>
            </div>
            <button class="new-tab-btn" id="newTabBtn">+</button>
        </div>

        <div class="toolbar">
            <div class="nav-buttons">
                <button class="nav-btn" id="backBtn" disabled>←</button>
                <button class="nav-btn" id="forwardBtn" disabled>→</button>
                <button class="nav-btn" id="reloadBtn">⟳</button>
            </div>
            
            <input type="text" class="address-bar" id="addressBar" placeholder="Enter URL or search...">
            
            <div class="privacy-indicator" id="privacyIndicator">
                <span class="status-indicator active"></span>
                Protected
            </div>
            
            <button class="nav-btn" id="privacyPanelBtn">⚙</button>
        </div>

        <div class="content-area">
            <webview id="webview" class="webview" src="about:blank" nodeintegration="false" websecurity="true"></webview>
            
            <div class="loading-indicator" id="loadingIndicator">
                <div class="spinner"></div>
            </div>

            <div class="privacy-panel" id="privacyPanel">
                <div class="privacy-panel-header">
                    <div class="privacy-panel-title">Privacy Control</div>
                    <div class="privacy-panel-subtitle">Advanced protection settings</div>
                </div>

                <div class="privacy-section">
                    <div class="privacy-section-title">Fingerprint Protection</div>
                    <div class="privacy-toggle">
                        <span>Canvas Protection</span>
                        <div class="toggle-switch active" data-setting="canvasProtection"></div>
                    </div>
                    <div class="privacy-toggle">
                        <span>WebGL Protection</span>
                        <div class="toggle-switch active" data-setting="webglProtection"></div>
                    </div>
                    <div class="privacy-toggle">
                        <span>Audio Protection</span>
                        <div class="toggle-switch active" data-setting="audioProtection"></div>
                    </div>
                </div>

                <div class="privacy-section">
                    <div class="privacy-section-title">Network Privacy</div>
                    <div class="privacy-toggle">
                        <span>Block Trackers</span>
                        <div class="toggle-switch active" data-setting="blockTrackers"></div>
                    </div>
                    <div class="privacy-toggle">
                        <span>Block Ads</span>
                        <div class="toggle-switch active" data-setting="blockAds"></div>
                    </div>
                    <div class="privacy-toggle">
                        <span>WebRTC Protection</span>
                        <div class="toggle-switch active" data-setting="webrtcProtection"></div>
                    </div>
                </div>

                <div class="privacy-section">
                    <div class="privacy-section-title">User Agent</div>
                    <div class="privacy-toggle">
                        <span>Auto Rotation</span>
                        <div class="toggle-switch active" data-setting="userAgentRotation"></div>
                    </div>
                    <button class="nav-btn" id="rotateUserAgentBtn" style="width: 100%; margin-top: 10px;">
                        Rotate Now
                    </button>
                </div>

                <div class="privacy-section">
                    <div class="privacy-section-title">Proxy Settings</div>
                    <div class="privacy-toggle">
                        <span>Proxy Enabled</span>
                        <div class="toggle-switch" data-setting="proxyEnabled"></div>
                    </div>
                    <div class="privacy-toggle">
                        <span>Auto Rotation</span>
                        <div class="toggle-switch" data-setting="proxyRotation"></div>
                    </div>
                </div>

                <div class="privacy-section">
                    <div class="privacy-section-title">Steganographic Features</div>
                    <div class="privacy-toggle">
                        <span>Traffic Obfuscation</span>
                        <div class="toggle-switch active" data-setting="trafficObfuscation"></div>
                    </div>
                    <div class="privacy-toggle">
                        <span>Timing Randomization</span>
                        <div class="toggle-switch active" data-setting="timingRandomization"></div>
                    </div>
                    <div class="privacy-toggle">
                        <span>Behavior Masking</span>
                        <div class="toggle-switch active" data-setting="behaviorMasking"></div>
                    </div>
                    <div class="privacy-toggle">
                        <span>Decoy Traffic</span>
                        <div class="toggle-switch active" data-setting="decoyTraffic"></div>
                    </div>
                    <div class="privacy-toggle">
                        <span>Request Padding</span>
                        <div class="toggle-switch active" data-setting="requestPadding"></div>
                    </div>
                    <div class="privacy-toggle">
                        <span>DNS Obfuscation</span>
                        <div class="toggle-switch active" data-setting="dnsObfuscation"></div>
                    </div>
                    <div style="margin-top: 10px;">
                        <label style="font-size: 12px; color: #666;">Obfuscation Intensity:</label>
                        <select id="obfuscationIntensity" style="width: 100%; margin-top: 5px; padding: 5px; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high" selected>High</option>
                            <option value="maximum">Maximum</option>
                        </select>
                    </div>
                </div>

                <div class="privacy-section">
                    <div class="privacy-section-title">Data Management</div>
                    <div class="privacy-action-buttons">
                        <button id="clearBrowsingDataBtn" class="privacy-action-btn danger">
                            <span class="btn-icon">🗑️</span>
                            <span class="btn-text">Clear Browsing Data</span>
                            <span class="btn-spinner" id="clearDataSpinner" style="display: none;">⏳</span>
                        </button>
                        <div class="action-description">
                            Remove all browsing data, cookies, cache, and stored information
                        </div>
                    </div>
                </div>

                <div class="privacy-section">
                    <div class="privacy-section-title">Session Statistics</div>
                    <div class="privacy-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="blockedTrackers">0</div>
                            <div class="stat-label">Trackers Blocked</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="blockedAds">0</div>
                            <div class="stat-label">Ads Blocked</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="fingerprintAttempts">0</div>
                            <div class="stat-label">Fingerprint Attempts</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="dataLeaks">0</div>
                            <div class="stat-label">Data Leaks Prevented</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="decoyRequests">0</div>
                            <div class="stat-label">Decoy Requests</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="obfuscatedPackets">0</div>
                            <div class="stat-label">Obfuscated Packets</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
