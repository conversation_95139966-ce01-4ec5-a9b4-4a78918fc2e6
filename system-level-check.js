/**
 * System-Level Diagnostic Check for Phantom Browser
 * Checks for potential system-level issues that could affect browsing
 */

const os = require('os');
const dns = require('dns');
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

console.log('🔍 System-Level Diagnostic Check');
console.log('=================================\n');

async function checkSystemLevel() {
    // 1. Check Windows Firewall Status
    console.log('🛡️ Checking Windows Firewall...');
    try {
        const { stdout } = await execAsync('netsh advfirewall show allprofiles state');
        const firewallEnabled = stdout.includes('State                                 ON');
        console.log(`  Firewall Status: ${firewallEnabled ? '🟡 Enabled' : '🟢 Disabled'}`);
        if (firewallEnabled) {
            console.log('  ℹ️ Firewall is enabled - may need exception for Phantom Browser');
        }
    } catch (error) {
        console.log('  ⚠️ Could not check firewall status:', error.message);
    }

    // 2. Check DNS Configuration
    console.log('\n🌐 Checking DNS Configuration...');
    try {
        const servers = dns.getServers();
        console.log(`  DNS Servers: ${servers.join(', ')}`);
        
        // Test DNS resolution
        const testDomain = 'google.com';
        const resolved = await new Promise((resolve, reject) => {
            dns.resolve4(testDomain, (err, addresses) => {
                if (err) reject(err);
                else resolve(addresses);
            });
        });
        console.log(`  ✅ DNS Resolution Test: ${testDomain} → ${resolved[0]}`);
    } catch (error) {
        console.log(`  ❌ DNS Resolution Failed: ${error.message}`);
    }

    // 3. Check Network Connectivity
    console.log('\n🔗 Checking Network Connectivity...');
    try {
        const { stdout } = await execAsync('ping -n 1 *******');
        const pingSuccess = stdout.includes('Reply from');
        console.log(`  Internet Connectivity: ${pingSuccess ? '✅ Working' : '❌ Failed'}`);
    } catch (error) {
        console.log('  ❌ Ping test failed:', error.message);
    }

    // 4. Check Proxy Settings
    console.log('\n🌐 Checking System Proxy Settings...');
    try {
        const { stdout } = await execAsync('netsh winhttp show proxy');
        const hasProxy = !stdout.includes('Direct access (no proxy server)');
        console.log(`  System Proxy: ${hasProxy ? '🟡 Configured' : '🟢 Direct'}`);
        if (hasProxy) {
            console.log('  ⚠️ System proxy detected - may conflict with Phantom Browser');
            console.log('  💡 Consider disabling system proxy or configuring Phantom Browser accordingly');
        }
    } catch (error) {
        console.log('  ⚠️ Could not check proxy settings:', error.message);
    }

    // 5. Check Antivirus/Security Software
    console.log('\n🛡️ Checking Security Software...');
    try {
        const { stdout } = await execAsync('wmic /namespace:\\\\root\\SecurityCenter2 path AntiVirusProduct get displayName');
        const antivirusProducts = stdout.split('\n').filter(line => line.trim() && !line.includes('displayName'));
        if (antivirusProducts.length > 0) {
            console.log('  Antivirus Products Detected:');
            antivirusProducts.forEach(product => {
                if (product.trim()) {
                    console.log(`    🟡 ${product.trim()}`);
                }
            });
            console.log('  ℹ️ Antivirus software may interfere with network requests');
            console.log('  💡 Consider adding Phantom Browser to antivirus whitelist');
        } else {
            console.log('  🟢 No antivirus products detected');
        }
    } catch (error) {
        console.log('  ⚠️ Could not check antivirus status:', error.message);
    }

    // 6. Check Network Adapters
    console.log('\n🔌 Checking Network Adapters...');
    try {
        const interfaces = os.networkInterfaces();
        const activeInterfaces = Object.keys(interfaces).filter(name => {
            return interfaces[name].some(iface => !iface.internal && iface.family === 'IPv4');
        });
        
        console.log(`  Active Network Interfaces: ${activeInterfaces.length}`);
        activeInterfaces.forEach(name => {
            const iface = interfaces[name].find(i => !i.internal && i.family === 'IPv4');
            console.log(`    ✅ ${name}: ${iface.address}`);
        });
    } catch (error) {
        console.log('  ❌ Could not check network adapters:', error.message);
    }

    // 7. Check Windows Version
    console.log('\n💻 System Information...');
    console.log(`  OS: ${os.type()} ${os.release()}`);
    console.log(`  Architecture: ${os.arch()}`);
    console.log(`  Node.js Version: ${process.version}`);

    // 8. Recommendations
    console.log('\n💡 SYSTEM-LEVEL RECOMMENDATIONS:');
    console.log('================================');
    console.log('Based on the system analysis:');
    console.log('');
    console.log('🔧 IMMEDIATE ACTIONS:');
    console.log('• Restart Phantom Browser to apply fixes');
    console.log('• Test browsing with simple websites first');
    console.log('• Check if network status shows "Direct Connection"');
    console.log('');
    console.log('🛡️ SECURITY SOFTWARE:');
    console.log('• Add Phantom Browser executable to antivirus whitelist');
    console.log('• Temporarily disable real-time protection for testing');
    console.log('• Check if firewall is blocking Electron applications');
    console.log('');
    console.log('🌐 NETWORK CONFIGURATION:');
    console.log('• Disable system proxy if not needed');
    console.log('• Use direct connection mode initially');
    console.log('• Test with different DNS servers if issues persist');
    console.log('');
    console.log('📊 MONITORING:');
    console.log('• Watch for console errors in Phantom Browser');
    console.log('• Monitor network status indicators');
    console.log('• Test browsing after each privacy feature change');

    console.log('\n✅ System-level diagnostic completed!');
    console.log('The primary issue was application-level (DoH configuration).');
    console.log('System-level factors appear normal.');
}

checkSystemLevel().catch(console.error);
