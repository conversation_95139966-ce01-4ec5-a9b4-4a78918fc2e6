import Store from 'electron-store';

export interface StoreSchema {
    privacy: {
        blockTrackers: boolean;
        blockAds: boolean;
        blockFingerprinting: boolean;
        spoofTimezone: boolean;
        spoofLanguage: boolean;
        spoofScreen: boolean;
        randomizeCanvasFingerprint: boolean;
        blockWebRTC: boolean;
        clearCookiesOnExit: boolean;
        useDoH: boolean;
    };
    security: {
        enableSandbox: boolean;
        blockDangerousDownloads: boolean;
        enableCSP: boolean;
        blockMixedContent: boolean;
        enableHSTS: boolean;
        blockPlugins: boolean;
        enableMemoryProtection: boolean;
        clearDataOnExit: boolean;
    };
    proxy: {
        currentProxy: {
            type: 'http' | 'https' | 'socks4' | 'socks5' | 'direct';
            host: string;
            port: number;
            username?: string;
            password?: string;
            enabled: boolean;
        } | null;
        proxyList: Array<{
            type: 'http' | 'https' | 'socks4' | 'socks5' | 'direct';
            host: string;
            port: number;
            username?: string;
            password?: string;
            enabled: boolean;
            name?: string;
            description?: string;
        }>;
        rotationEnabled: boolean;
        rotationInterval: number;
    };
    search: {
        defaultProvider: string;
        enableSuggestions: boolean;
        enableHistory: boolean;
        maxSuggestions: number;
        privacyMode: 'strict' | 'balanced' | 'standard';
        customProviders: Array<{
            id: string;
            name: string;
            baseUrl: string;
            searchUrl: string;
            suggestionsUrl?: string;
            icon?: string;
            privacyRating: number;
            features: string[];
            description: string;
        }>;
    };
    steganographic: {
        enableTrafficObfuscation: boolean;
        enableTimingRandomization: boolean;
        enableBehaviorMasking: boolean;
        enableDecoyTraffic: boolean;
        obfuscationIntensity: 'low' | 'medium' | 'high' | 'maximum';
        enableDPIEvasion: boolean;
        enableQuantumResistance: boolean;
        enableCrossPlatformCoordination: boolean;
    };
}

export class SettingsStore {
    private store: Store<StoreSchema>;
    private static instance: SettingsStore;

    private constructor() {
        this.store = new Store<StoreSchema>({
            name: 'phantom-browser-settings',
            defaults: this.getDefaultSettings(),
            schema: {
                privacy: {
                    type: 'object',
                    properties: {
                        blockTrackers: { type: 'boolean' },
                        blockAds: { type: 'boolean' },
                        blockFingerprinting: { type: 'boolean' },
                        spoofTimezone: { type: 'boolean' },
                        spoofLanguage: { type: 'boolean' },
                        spoofScreen: { type: 'boolean' },
                        randomizeCanvasFingerprint: { type: 'boolean' },
                        blockWebRTC: { type: 'boolean' },
                        clearCookiesOnExit: { type: 'boolean' },
                        useDoH: { type: 'boolean' }
                    }
                },
                security: {
                    type: 'object',
                    properties: {
                        enableSandbox: { type: 'boolean' },
                        blockDangerousDownloads: { type: 'boolean' },
                        enableCSP: { type: 'boolean' },
                        blockMixedContent: { type: 'boolean' },
                        enableHSTS: { type: 'boolean' },
                        blockPlugins: { type: 'boolean' },
                        enableMemoryProtection: { type: 'boolean' },
                        clearDataOnExit: { type: 'boolean' }
                    }
                },
                proxy: {
                    type: 'object',
                    properties: {
                        currentProxy: { type: ['object', 'null'] },
                        proxyList: { type: 'array' },
                        rotationEnabled: { type: 'boolean' },
                        rotationInterval: { type: 'number' }
                    }
                },
                search: {
                    type: 'object',
                    properties: {
                        defaultProvider: { type: 'string' },
                        enableSuggestions: { type: 'boolean' },
                        enableHistory: { type: 'boolean' },
                        maxSuggestions: { type: 'number' },
                        privacyMode: { type: 'string', enum: ['strict', 'balanced', 'standard'] },
                        customProviders: { type: 'array' }
                    }
                },
                steganographic: {
                    type: 'object',
                    properties: {
                        enableTrafficObfuscation: { type: 'boolean' },
                        enableTimingRandomization: { type: 'boolean' },
                        enableBehaviorMasking: { type: 'boolean' },
                        enableDecoyTraffic: { type: 'boolean' },
                        obfuscationIntensity: { type: 'string', enum: ['low', 'medium', 'high', 'maximum'] },
                        enableDPIEvasion: { type: 'boolean' },
                        enableQuantumResistance: { type: 'boolean' },
                        enableCrossPlatformCoordination: { type: 'boolean' }
                    }
                }
            }
        });
    }

    public static getInstance(): SettingsStore {
        if (!SettingsStore.instance) {
            SettingsStore.instance = new SettingsStore();
        }
        return SettingsStore.instance;
    }

    private getDefaultSettings(): StoreSchema {
        return {
            privacy: {
                blockTrackers: true,
                blockAds: true,
                blockFingerprinting: true,
                spoofTimezone: true,
                spoofLanguage: true,
                spoofScreen: true,
                randomizeCanvasFingerprint: true,
                blockWebRTC: true,
                clearCookiesOnExit: true,
                useDoH: true
            },
            security: {
                enableSandbox: true,
                blockDangerousDownloads: true,
                enableCSP: true,
                blockMixedContent: true,
                enableHSTS: true,
                blockPlugins: true,
                enableMemoryProtection: true,
                clearDataOnExit: true
            },
            proxy: {
                currentProxy: null,
                proxyList: [
                    {
                        type: 'direct',
                        host: '',
                        port: 0,
                        enabled: true,
                        name: 'Direct Connection',
                        description: 'No proxy - direct internet connection'
                    }
                ],
                rotationEnabled: false,
                rotationInterval: 10
            },
            search: {
                defaultProvider: 'duckduckgo',
                enableSuggestions: true,
                enableHistory: true,
                maxSuggestions: 8,
                privacyMode: 'balanced',
                customProviders: []
            },
            steganographic: {
                enableTrafficObfuscation: true,
                enableTimingRandomization: true,
                enableBehaviorMasking: true,
                enableDecoyTraffic: true,
                obfuscationIntensity: 'high',
                enableDPIEvasion: true,
                enableQuantumResistance: true,
                enableCrossPlatformCoordination: true
            }
        };
    }

    // Generic methods for accessing settings
    public get<K extends keyof StoreSchema>(key: K): StoreSchema[K] {
        try {
            return this.store.get(key);
        } catch (error) {
            console.error(`Failed to get setting ${key}:`, error);
            return this.getDefaultSettings()[key];
        }
    }

    public set<K extends keyof StoreSchema>(key: K, value: StoreSchema[K]): void {
        try {
            this.store.set(key, value);
            console.log(`Settings saved for ${key}`);
        } catch (error) {
            console.error(`Failed to save setting ${key}:`, error);
            throw new Error(`Failed to save ${key} settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    public has<K extends keyof StoreSchema>(key: K): boolean {
        return this.store.has(key);
    }

    public delete<K extends keyof StoreSchema>(key: K): void {
        try {
            this.store.delete(key);
            console.log(`Settings deleted for ${key}`);
        } catch (error) {
            console.error(`Failed to delete setting ${key}:`, error);
            throw new Error(`Failed to delete ${key} settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    public clear(): void {
        try {
            this.store.clear();
            console.log('All settings cleared');
        } catch (error) {
            console.error('Failed to clear settings:', error);
            throw new Error(`Failed to clear settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    public getStorePath(): string {
        return this.store.path;
    }

    public reset(): void {
        try {
            this.store.store = this.getDefaultSettings();
            console.log('Settings reset to defaults');
        } catch (error) {
            console.error('Failed to reset settings:', error);
            throw new Error(`Failed to reset settings: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    // Specific helper methods for common operations
    public updatePrivacySettings(settings: Partial<StoreSchema['privacy']>): void {
        const currentSettings = this.get('privacy');
        this.set('privacy', { ...currentSettings, ...settings });
    }

    public updateSecuritySettings(settings: Partial<StoreSchema['security']>): void {
        const currentSettings = this.get('security');
        this.set('security', { ...currentSettings, ...settings });
    }

    public updateProxySettings(settings: Partial<StoreSchema['proxy']>): void {
        const currentSettings = this.get('proxy');
        this.set('proxy', { ...currentSettings, ...settings });
    }

    public updateSearchSettings(settings: Partial<StoreSchema['search']>): void {
        const currentSettings = this.get('search');
        this.set('search', { ...currentSettings, ...settings });
    }

    public updateSteganographicSettings(settings: Partial<StoreSchema['steganographic']>): void {
        const currentSettings = this.get('steganographic');
        this.set('steganographic', { ...currentSettings, ...settings });
    }
}

export default SettingsStore;
