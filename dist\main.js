"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const PrivacyManager_1 = require("./privacy/PrivacyManager");
const ProxyManager_1 = require("./network/ProxyManager");
const NetworkConfigManager_1 = require("./network/NetworkConfigManager");
const FingerprintProtection_1 = require("./privacy/FingerprintProtection");
const SecureLogger_1 = require("./utils/SecureLogger");
const UserAgentManager_1 = require("./privacy/UserAgentManager");
const SecurityManager_1 = require("./security/SecurityManager");
const SteganographicManager_1 = require("./steganography/SteganographicManager");
const TrafficAnalysisProtection_1 = require("./steganography/TrafficAnalysisProtection");
const BehavioralObfuscation_1 = require("./steganography/BehavioralObfuscation");
const BehavioralAI_1 = require("./ai/BehavioralAI");
const ThreatAdaptationEngine_1 = require("./threat/ThreatAdaptationEngine");
const AdvancedDPIEvasion_1 = require("./dpi/AdvancedDPIEvasion");
const BiometricMimicry_1 = require("./biometric/BiometricMimicry");
const DistributedDecoyNetwork_1 = require("./network/DistributedDecoyNetwork");
const QuantumResistantObfuscation_1 = require("./quantum/QuantumResistantObfuscation");
const CrossPlatformCoordinator_1 = require("./crossplatform/CrossPlatformCoordinator");
class PhantomBrowser {
    constructor() {
        this.mainWindow = null;
        this.privacyManager = new PrivacyManager_1.PrivacyManager();
        this.proxyManager = new ProxyManager_1.ProxyManager();
        this.networkConfigManager = new NetworkConfigManager_1.NetworkConfigManager(this.proxyManager);
        this.fingerprintProtection = new FingerprintProtection_1.FingerprintProtection();
        this.userAgentManager = new UserAgentManager_1.UserAgentManager();
        this.securityManager = new SecurityManager_1.SecurityManager();
        this.steganographicManager = new SteganographicManager_1.SteganographicManager();
        this.trafficAnalysisProtection = new TrafficAnalysisProtection_1.TrafficAnalysisProtection();
        this.behavioralObfuscation = new BehavioralObfuscation_1.BehavioralObfuscation();
        this.behavioralAI = new BehavioralAI_1.BehavioralAI();
        this.threatAdaptationEngine = new ThreatAdaptationEngine_1.ThreatAdaptationEngine();
        this.advancedDPIEvasion = new AdvancedDPIEvasion_1.AdvancedDPIEvasion();
        this.biometricMimicry = new BiometricMimicry_1.BiometricMimicry();
        this.distributedDecoyNetwork = new DistributedDecoyNetwork_1.DistributedDecoyNetwork();
        this.quantumResistantObfuscation = new QuantumResistantObfuscation_1.QuantumResistantObfuscation();
        this.crossPlatformCoordinator = new CrossPlatformCoordinator_1.CrossPlatformCoordinator();
    }
    async initialize() {
        await electron_1.app.whenReady();
        // Configure security settings first
        await this.configureSecuritySettings();
        // Create main window immediately for better user experience
        this.createMainWindow();
        this.setupEventHandlers();
        this.setupMenu();
        // Initialize core privacy components first (critical path)
        console.log('Initializing core privacy components...');
        await Promise.all([
            this.privacyManager.initialize(),
            this.fingerprintProtection.initialize(),
            this.userAgentManager.initialize()
        ]);
        // Initialize steganographic features (medium priority)
        console.log('Initializing steganographic features...');
        setTimeout(async () => {
            try {
                await Promise.all([
                    this.steganographicManager.initialize(),
                    this.trafficAnalysisProtection.initialize(),
                    this.behavioralObfuscation.initialize()
                ]);
                console.log('Steganographic features initialized');
            }
            catch (error) {
                console.error('Failed to initialize steganographic features:', error);
            }
        }, 1000);
        // Initialize advanced features in background (low priority)
        console.log('Initializing advanced features in background...');
        setTimeout(async () => {
            try {
                await Promise.all([
                    this.behavioralAI.initialize(),
                    this.threatAdaptationEngine.initialize(),
                    this.advancedDPIEvasion.initialize(),
                    this.biometricMimicry.initialize(),
                    this.distributedDecoyNetwork.initialize(),
                    this.quantumResistantObfuscation.initialize(),
                    this.crossPlatformCoordinator.initialize()
                ]);
                console.log('All advanced features initialized');
                // Notify renderer that all features are ready
                if (this.mainWindow) {
                    this.mainWindow.webContents.send('advanced-features-ready');
                }
            }
            catch (error) {
                console.error('Failed to initialize advanced features:', error);
            }
        }, 3000);
    }
    async configureSecuritySettings() {
        // Configure session security
        const ses = electron_1.session.defaultSession;
        // Block dangerous permissions
        ses.setPermissionRequestHandler((_webContents, permission, callback) => {
            const deniedPermissions = ['camera', 'microphone', 'geolocation', 'notifications'];
            callback(!deniedPermissions.includes(permission));
        });
        // Configure security headers
        ses.webRequest.onHeadersReceived((details, callback) => {
            const responseHeaders = details.responseHeaders || {};
            // Add security headers
            responseHeaders['X-Frame-Options'] = ['DENY'];
            responseHeaders['X-Content-Type-Options'] = ['nosniff'];
            responseHeaders['Referrer-Policy'] = ['no-referrer'];
            callback({ responseHeaders });
        });
        // Block tracking and fingerprinting scripts
        await this.setupContentBlocking();
    }
    async setupContentBlocking() {
        const ses = electron_1.session.defaultSession;
        // Block known tracking domains
        const trackingDomains = [
            '*://google-analytics.com/*',
            '*://googletagmanager.com/*',
            '*://facebook.com/tr/*',
            '*://doubleclick.net/*',
            '*://googlesyndication.com/*'
        ];
        ses.webRequest.onBeforeRequest({ urls: trackingDomains }, (_details, callback) => {
            callback({ cancel: true });
        });
    }
    createMainWindow() {
        console.log('Creating main window...');
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 800,
            minHeight: 600,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, 'preload.js'),
                sandbox: false, // Keep disabled for webview functionality
                webSecurity: true, // Enable web security
                allowRunningInsecureContent: false,
                experimentalFeatures: false,
                disableBlinkFeatures: 'Auxclick' // Disable auxiliary click for security
            },
            titleBarStyle: 'default', // Changed from 'hidden' to 'default' for better visibility
            show: true, // Changed to true to force immediate visibility
            backgroundColor: '#ffffff',
            center: true, // Center the window on screen
            resizable: true,
            maximizable: true,
            minimizable: true,
            closable: true,
            alwaysOnTop: false,
            skipTaskbar: false
        });
        console.log('Window created, loading HTML file...');
        const htmlPath = path.join(__dirname, '../renderer/index.html');
        console.log('HTML path:', htmlPath);
        this.mainWindow.loadFile(htmlPath).then(() => {
            console.log('HTML file loaded successfully');
            this.mainWindow?.show();
            this.mainWindow?.focus();
        }).catch((error) => {
            console.error('Failed to load HTML file:', error);
            // Load a simple HTML content as fallback
            this.mainWindow?.loadURL('data:text/html,<html><head><title>Phantom Browser</title></head><body><h1>Phantom Browser</h1><p>Advanced Privacy Protection Active</p><p>HTML file loading failed, but the application is running.</p></body></html>');
            this.mainWindow?.show();
            this.mainWindow?.focus();
        });
        // Add multiple event handlers for debugging
        this.mainWindow.once('ready-to-show', () => {
            console.log('Window ready-to-show event fired');
            this.mainWindow?.show();
            this.mainWindow?.focus();
            console.log('Window shown and focused');
        });
        this.mainWindow.on('closed', () => {
            console.log('Window closed');
            this.mainWindow = null;
        });
        this.mainWindow.on('show', () => {
            console.log('Window show event fired');
        });
        this.mainWindow.on('focus', () => {
            console.log('Window focus event fired');
        });
        // Add webContents event handlers for debugging
        this.mainWindow.webContents.on('did-finish-load', () => {
            console.log('WebContents did-finish-load event fired');
        });
        this.mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
            console.error('WebContents did-fail-load:', errorCode, errorDescription);
        });
        this.mainWindow.webContents.on('dom-ready', () => {
            console.log('WebContents DOM ready');
        });
        // Prevent new window creation
        this.mainWindow.webContents.setWindowOpenHandler(() => {
            return { action: 'deny' };
        });
    }
    setupEventHandlers() {
        electron_1.app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        electron_1.app.on('activate', () => {
            if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                this.createMainWindow();
            }
        });
        // Handle external links
        electron_1.app.on('web-contents-created', (_event, contents) => {
            contents.setWindowOpenHandler(({ url }) => {
                electron_1.shell.openExternal(url);
                return { action: 'deny' };
            });
        });
        // Setup IPC handlers
        this.setupIpcHandlers();
    }
    setupIpcHandlers() {
        // Navigation handlers
        electron_1.ipcMain.handle('navigate', async (event, url) => {
            console.log('Navigate called with URL:', url);
            if (this.mainWindow) {
                try {
                    // Send navigation command to renderer to handle webview
                    this.mainWindow.webContents.send('navigate-webview', url);
                    return { success: true };
                }
                catch (error) {
                    console.error('Navigation failed:', error);
                    return { success: false, error: String(error) };
                }
            }
            return { success: false, error: 'No main window' };
        });
        electron_1.ipcMain.handle('go-back', async () => {
            if (this.mainWindow?.webContents.canGoBack()) {
                this.mainWindow.webContents.goBack();
            }
        });
        electron_1.ipcMain.handle('go-forward', async () => {
            if (this.mainWindow?.webContents.canGoForward()) {
                this.mainWindow.webContents.goForward();
            }
        });
        electron_1.ipcMain.handle('reload', async () => {
            this.mainWindow?.webContents.reload();
        });
        // Privacy settings handlers
        electron_1.ipcMain.handle('get-privacy-settings', async () => {
            return this.privacyManager.getSettings();
        });
        electron_1.ipcMain.handle('update-privacy-settings', async (_, settings) => {
            SecureLogger_1.secureLogger.info('Updating privacy settings', 'Main');
            this.privacyManager.updateSettings(settings);
            return { success: true };
        });
        // Clear browsing data handler - CRITICAL FIX
        electron_1.ipcMain.handle('clear-browsing-data', async () => {
            console.log('Clearing browsing data...');
            try {
                // Clear privacy data using PrivacyManager
                const privacyResult = await this.privacyManager.clearPrivacyData();
                if (!privacyResult.success) {
                    console.error('Privacy data clearing failed:', privacyResult.error);
                }
                // Clear security data using SecurityManager
                const securityResult = await this.securityManager.clearSecurityData();
                if (!securityResult.success) {
                    console.error('Security data clearing failed:', securityResult.error);
                }
                // Check if both operations succeeded
                if (privacyResult.success && securityResult.success) {
                    console.log('All browsing data cleared successfully');
                    return {
                        success: true,
                        message: 'All browsing data has been cleared successfully',
                        timestamp: Date.now()
                    };
                }
                else {
                    // Partial failure
                    const errors = [];
                    if (!privacyResult.success)
                        errors.push(`Privacy: ${privacyResult.error}`);
                    if (!securityResult.success)
                        errors.push(`Security: ${securityResult.error}`);
                    return {
                        success: false,
                        error: 'Some browsing data could not be cleared',
                        details: errors.join('; '),
                        timestamp: Date.now()
                    };
                }
            }
            catch (error) {
                console.error('Unexpected error during browsing data clearing:', error);
                return {
                    success: false,
                    error: 'An unexpected error occurred while clearing browsing data',
                    details: error instanceof Error ? error.message : String(error),
                    timestamp: Date.now()
                };
            }
        });
        // Steganographic settings handlers
        electron_1.ipcMain.handle('get-steganographic-settings', async () => {
            return this.steganographicManager.getSettings();
        });
        electron_1.ipcMain.handle('update-steganographic-settings', async (event, settings) => {
            console.log('Updating steganographic settings:', settings);
            this.steganographicManager.updateSettings(settings);
            return { success: true };
        });
        // User agent handlers
        electron_1.ipcMain.handle('get-user-agent-profile', async () => {
            return this.userAgentManager.getCurrentProfile();
        });
        electron_1.ipcMain.handle('rotate-user-agent', async () => {
            this.userAgentManager.rotateUserAgent();
            return { success: true };
        });
        electron_1.ipcMain.handle('enable-user-agent-rotation', async () => {
            this.userAgentManager.enableRotation();
            return { success: true };
        });
        electron_1.ipcMain.handle('disable-user-agent-rotation', async () => {
            this.userAgentManager.disableRotation();
            return { success: true };
        });
        // Proxy handlers
        electron_1.ipcMain.handle('get-proxy-settings', async () => {
            return {
                current: this.proxyManager.getCurrentProxy(),
                list: this.proxyManager.getProxyList(),
                enabled: this.proxyManager.getCurrentProxy()?.enabled || false
            };
        });
        electron_1.ipcMain.handle('set-proxy', async (event, config) => {
            console.log('Setting proxy:', config);
            await this.proxyManager.setProxy(config);
            return { success: true };
        });
        electron_1.ipcMain.handle('clear-proxy', async () => {
            await this.proxyManager.clearProxy();
            return { success: true };
        });
        electron_1.ipcMain.handle('enable-proxy-rotation', async (event, interval) => {
            this.proxyManager.enableProxyRotation(interval);
            return { success: true };
        });
        electron_1.ipcMain.handle('disable-proxy-rotation', async () => {
            this.proxyManager.disableProxyRotation();
            return { success: true };
        });
        // Enhanced proxy management handlers
        electron_1.ipcMain.handle('add-proxy', async (_, config) => {
            SecureLogger_1.secureLogger.info('Adding proxy configuration', 'Main');
            return await this.proxyManager.addProxy(config);
        });
        electron_1.ipcMain.handle('remove-proxy', async (_, host, port) => {
            SecureLogger_1.secureLogger.info(`Removing proxy ${host}:${port}`, 'Main');
            return this.proxyManager.removeProxy(host, port);
        });
        electron_1.ipcMain.handle('update-proxy', async (_, oldHost, oldPort, newConfig) => {
            SecureLogger_1.secureLogger.info(`Updating proxy ${oldHost}:${oldPort}`, 'Main');
            return await this.proxyManager.updateProxy(oldHost, oldPort, newConfig);
        });
        electron_1.ipcMain.handle('test-proxy', async (_, config) => {
            SecureLogger_1.secureLogger.info('Testing proxy configuration', 'Main');
            try {
                const isWorking = await this.proxyManager.testProxy(config);
                return {
                    success: true,
                    working: isWorking,
                    message: isWorking ? 'Proxy is working' : 'Proxy connection failed'
                };
            }
            catch (error) {
                return {
                    success: false,
                    working: false,
                    error: error instanceof Error ? error.message : String(error)
                };
            }
        });
        electron_1.ipcMain.handle('validate-proxy', async (_, config) => {
            SecureLogger_1.secureLogger.info('Validating proxy configuration', 'Main');
            return this.proxyManager.validateProxyConfig(config);
        });
        // Network configuration management handlers
        electron_1.ipcMain.handle('get-network-config', async () => {
            return this.networkConfigManager.getCurrentConfiguration();
        });
        electron_1.ipcMain.handle('set-network-config', async (_, config) => {
            console.log('Setting network configuration:', config);
            return await this.networkConfigManager.setNetworkConfiguration(config);
        });
        electron_1.ipcMain.handle('switch-to-proxy', async (_, proxyConfig) => {
            console.log('Switching to proxy mode:', proxyConfig);
            return await this.networkConfigManager.switchToProxy(proxyConfig);
        });
        electron_1.ipcMain.handle('switch-to-doh', async (_, providers) => {
            console.log('Switching to DoH mode:', providers);
            return await this.networkConfigManager.switchToDoH(providers);
        });
        electron_1.ipcMain.handle('switch-to-direct', async () => {
            console.log('Switching to direct connection mode');
            return await this.networkConfigManager.switchToDirect();
        });
        electron_1.ipcMain.handle('get-network-status', async () => {
            return this.networkConfigManager.getNetworkStatus();
        });
        electron_1.ipcMain.handle('get-doh-providers', async () => {
            return this.networkConfigManager.getDoHProviders();
        });
        // Additional steganographic handlers
        electron_1.ipcMain.handle('enable-traffic-obfuscation', async () => {
            try {
                await this.proxyManager.setupTrafficObfuscation();
                const settings = this.steganographicManager.getSettings();
                settings.enableTrafficObfuscation = true;
                this.steganographicManager.updateSettings(settings);
                return { success: true };
            }
            catch (error) {
                console.error('Failed to enable traffic obfuscation:', error);
                return { success: false, error: String(error) };
            }
        });
        electron_1.ipcMain.handle('disable-traffic-obfuscation', async () => {
            try {
                const settings = this.steganographicManager.getSettings();
                settings.enableTrafficObfuscation = false;
                this.steganographicManager.updateSettings(settings);
                return { success: true };
            }
            catch (error) {
                console.error('Failed to disable traffic obfuscation:', error);
                return { success: false, error: String(error) };
            }
        });
        electron_1.ipcMain.handle('enable-decoy-traffic', async () => {
            try {
                const settings = this.steganographicManager.getSettings();
                settings.enableDecoyTraffic = true;
                this.steganographicManager.updateSettings(settings);
                // The distributed decoy network is always running, just enable the setting
                console.log('Decoy traffic enabled');
                return { success: true };
            }
            catch (error) {
                console.error('Failed to enable decoy traffic:', error);
                return { success: false, error: String(error) };
            }
        });
        electron_1.ipcMain.handle('disable-decoy-traffic', async () => {
            try {
                const settings = this.steganographicManager.getSettings();
                settings.enableDecoyTraffic = false;
                this.steganographicManager.updateSettings(settings);
                console.log('Decoy traffic disabled');
                return { success: true };
            }
            catch (error) {
                console.error('Failed to disable decoy traffic:', error);
                return { success: false, error: String(error) };
            }
        });
        electron_1.ipcMain.handle('enable-behavior-masking', async () => {
            try {
                const settings = this.steganographicManager.getSettings();
                settings.enableBehaviorMasking = true;
                this.steganographicManager.updateSettings(settings);
                // Behavioral obfuscation is controlled via settings
                console.log('Behavior masking enabled');
                return { success: true };
            }
            catch (error) {
                console.error('Failed to enable behavior masking:', error);
                return { success: false, error: String(error) };
            }
        });
        electron_1.ipcMain.handle('disable-behavior-masking', async () => {
            try {
                const settings = this.steganographicManager.getSettings();
                settings.enableBehaviorMasking = false;
                this.steganographicManager.updateSettings(settings);
                console.log('Behavior masking disabled');
                return { success: true };
            }
            catch (error) {
                console.error('Failed to disable behavior masking:', error);
                return { success: false, error: String(error) };
            }
        });
        electron_1.ipcMain.handle('enable-timing-randomization', async () => {
            try {
                const settings = this.steganographicManager.getSettings();
                settings.enableTimingRandomization = true;
                this.steganographicManager.updateSettings(settings);
                console.log('Timing randomization enabled');
                return { success: true };
            }
            catch (error) {
                console.error('Failed to enable timing randomization:', error);
                return { success: false, error: String(error) };
            }
        });
        electron_1.ipcMain.handle('disable-timing-randomization', async () => {
            try {
                const settings = this.steganographicManager.getSettings();
                settings.enableTimingRandomization = false;
                this.steganographicManager.updateSettings(settings);
                console.log('Timing randomization disabled');
                return { success: true };
            }
            catch (error) {
                console.error('Failed to disable timing randomization:', error);
                return { success: false, error: String(error) };
            }
        });
        // Fingerprint protection handlers
        electron_1.ipcMain.handle('get-fingerprint-profile', async () => {
            return this.fingerprintProtection.getCurrentProfile();
        });
        electron_1.ipcMain.handle('set-fingerprint-profile', async (_event, profile) => {
            try {
                // FingerprintProtection doesn't have a public setProfile method
                // The profile rotation is handled automatically
                console.log('Fingerprint profile setting requested:', profile);
                return { success: true, message: 'Fingerprint profiles are rotated automatically' };
            }
            catch (error) {
                console.error('Failed to set fingerprint profile:', error);
                return { success: false, error: String(error) };
            }
        });
        electron_1.ipcMain.handle('rotate-fingerprint-profile', async () => {
            try {
                // Trigger a manual rotation by restarting the rotation
                await this.fingerprintProtection.initialize();
                return { success: true };
            }
            catch (error) {
                console.error('Failed to rotate fingerprint profile:', error);
                return { success: false, error: String(error) };
            }
        });
        // Security settings handlers
        electron_1.ipcMain.handle('get-security-settings', async () => {
            return this.securityManager.getSettings();
        });
        electron_1.ipcMain.handle('update-security-settings', async (_event, settings) => {
            try {
                this.securityManager.updateSettings(settings);
                return { success: true };
            }
            catch (error) {
                console.error('Failed to update security settings:', error);
                return { success: false, error: String(error) };
            }
        });
        electron_1.ipcMain.handle('perform-security-audit', async () => {
            try {
                const auditResults = await this.securityManager.performSecurityAudit();
                return { success: true, results: auditResults };
            }
            catch (error) {
                console.error('Failed to perform security audit:', error);
                return { success: false, error: String(error) };
            }
        });
        // Additional steganographic data handlers
        electron_1.ipcMain.handle('get-traffic-analysis-stats', async () => {
            return this.trafficAnalysisProtection.getTrafficStatistics();
        });
        electron_1.ipcMain.handle('get-behavioral-profile', async () => {
            return this.behavioralObfuscation.getCurrentProfile();
        });
        electron_1.ipcMain.handle('set-behavioral-profile', async (_event, profile) => {
            try {
                // The behavioral obfuscation will handle profile setting internally
                console.log('Behavioral profile update requested:', profile);
                return { success: true };
            }
            catch (error) {
                console.error('Failed to set behavioral profile:', error);
                return { success: false, error: String(error) };
            }
        });
        electron_1.ipcMain.handle('get-activity-statistics', async () => {
            return this.behavioralObfuscation.getActivityStatistics();
        });
        // Utility handlers
        electron_1.ipcMain.handle('open-dev-tools', async () => {
            this.mainWindow?.webContents.openDevTools();
        });
        electron_1.ipcMain.handle('close-dev-tools', async () => {
            this.mainWindow?.webContents.closeDevTools();
        });
        console.log('IPC handlers setup complete');
    }
    setupMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'New Tab',
                        accelerator: 'CmdOrCtrl+T',
                        click: () => this.createNewTab()
                    },
                    {
                        label: 'New Private Window',
                        accelerator: 'CmdOrCtrl+Shift+N',
                        click: () => this.createPrivateWindow()
                    },
                    { type: 'separator' },
                    {
                        label: 'Exit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => electron_1.app.quit()
                    }
                ]
            },
            {
                label: 'Privacy',
                submenu: [
                    {
                        label: 'Clear Browsing Data',
                        click: () => this.clearBrowsingData()
                    },
                    {
                        label: 'Privacy Settings',
                        click: () => this.openPrivacySettings()
                    },
                    {
                        label: 'Proxy Settings',
                        click: () => this.openProxySettings()
                    }
                ]
            }
        ];
        const menu = electron_1.Menu.buildFromTemplate(template);
        electron_1.Menu.setApplicationMenu(menu);
    }
    createNewTab() {
        // Implementation for new tab
        this.mainWindow?.webContents.send('create-new-tab');
    }
    createPrivateWindow() {
        // Implementation for private window
        // This would create a new window with enhanced privacy settings
    }
    async clearBrowsingData() {
        const ses = electron_1.session.defaultSession;
        await ses.clearStorageData();
        await ses.clearCache();
    }
    openPrivacySettings() {
        this.mainWindow?.webContents.send('open-privacy-settings');
    }
    openProxySettings() {
        this.mainWindow?.webContents.send('open-proxy-settings');
    }
}
// Initialize the browser
const phantomBrowser = new PhantomBrowser();
phantomBrowser.initialize().catch(console.error);
// Handle app events
electron_1.app.on('before-quit', async () => {
    // Cleanup operations
    await electron_1.session.defaultSession.clearStorageData();
});
//# sourceMappingURL=main.js.map