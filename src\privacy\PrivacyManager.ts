import { session, webContents } from 'electron';
import * as crypto from 'crypto';
import { SettingsStore } from '../utils/SettingsStore';
import { ErrorHandler, ErrorCode, ApiResponse } from '../utils/ErrorHandler';
import { secureLogger } from '../utils/SecureLogger';

export interface PrivacySettings {
    blockTrackers: boolean;
    blockAds: boolean;
    blockFingerprinting: boolean;
    spoofTimezone: boolean;
    spoofLanguage: boolean;
    spoofScreen: boolean;
    randomizeCanvasFingerprint: boolean;
    blockWebRTC: boolean;
    clearCookiesOnExit: boolean;
    useDoH: boolean;
}

export class PrivacyManager {
    private settings!: PrivacySettings;
    private trackingDomains: Set<string>;
    private adBlockRules: string[];
    private settingsStore: SettingsStore;
    private errorHandler: ErrorHandler;

    constructor() {
        this.settingsStore = SettingsStore.getInstance();
        this.errorHandler = ErrorHandler.getInstance();
        this.trackingDomains = new Set();
        this.adBlockRules = [];

        // Load settings from persistent storage
        this.loadSettings();
    }

    async initialize(): Promise<void> {
        await this.loadBlockLists();
        await this.configureSession();
        this.setupRequestInterception();
        this.setupScriptInjection();
    }

    private async loadBlockLists(): Promise<void> {
        // Load tracking domains
        const trackingDomains = [
            'google-analytics.com',
            'googletagmanager.com',
            'facebook.com',
            'doubleclick.net',
            'googlesyndication.com',
            'amazon-adsystem.com',
            'adsystem.amazon.com',
            'scorecardresearch.com',
            'quantserve.com',
            'outbrain.com',
            'taboola.com',
            'addthis.com',
            'sharethis.com',
            'chartbeat.com',
            'hotjar.com',
            'fullstory.com',
            'mouseflow.com',
            'crazyegg.com',
            'mixpanel.com',
            'segment.com',
            'amplitude.com'
        ];

        trackingDomains.forEach(domain => this.trackingDomains.add(domain));

        // Load ad block rules (simplified EasyList format)
        this.adBlockRules = [
            '||googleadservices.com^',
            '||googlesyndication.com^',
            '||doubleclick.net^',
            '||amazon-adsystem.com^',
            '||facebook.com/tr^',
            '||analytics.google.com^',
            '||google-analytics.com^',
            '||googletagmanager.com^'
        ];
    }

    private async configureSession(): Promise<void> {
        const ses = session.defaultSession;

        // Configure DNS over HTTPS
        if (this.settings.useDoH) {
            ses.setProxy({
                mode: 'pac_script',
                pacScript: this.generatePACScript()
            });
        }

        // Block third-party cookies
        ses.cookies.set({
            url: 'https://example.com',
            name: 'SameSite',
            value: 'Strict'
        });

        // Configure user agent
        ses.setUserAgent(this.generateRandomUserAgent());
    }

    private setupRequestInterception(): void {
        const ses = session.defaultSession;

        // Block tracking requests
        ses.webRequest.onBeforeRequest({ urls: ['<all_urls>'] }, (details, callback) => {
            const url = new URL(details.url);
            
            // Check if domain is in tracking list
            if (this.settings.blockTrackers && this.isTrackingDomain(url.hostname)) {
                callback({ cancel: true });
                return;
            }

            // Check ad block rules
            if (this.settings.blockAds && this.matchesAdBlockRule(details.url)) {
                callback({ cancel: true });
                return;
            }

            callback({ cancel: false });
        });

        // Modify headers for privacy
        ses.webRequest.onBeforeSendHeaders({ urls: ['<all_urls>'] }, (details, callback) => {
            const headers = details.requestHeaders;

            // Remove tracking headers
            delete headers['X-Requested-With'];
            delete headers['X-Forwarded-For'];
            delete headers['X-Real-IP'];

            // Spoof referrer
            if (headers['Referer']) {
                headers['Referer'] = this.spoofReferrer(details.url);
            }

            // Add privacy headers
            headers['DNT'] = '1';
            headers['Sec-GPC'] = '1';

            callback({ requestHeaders: headers });
        });
    }

    private setupScriptInjection(): void {
        const ses = session.defaultSession;

        // Inject privacy protection scripts
        ses.webRequest.onHeadersReceived({ urls: ['<all_urls>'] }, (details, callback) => {
            if (details.responseHeaders && details.responseHeaders['content-type']?.[0]?.includes('text/html')) {
                // Inject fingerprinting protection script
                const script = this.generateProtectionScript();
                details.responseHeaders['X-Privacy-Script'] = [script];
            }

            callback({ responseHeaders: details.responseHeaders });
        });
    }

    private isTrackingDomain(hostname: string): boolean {
        return Array.from(this.trackingDomains).some(domain => 
            hostname === domain || hostname.endsWith('.' + domain)
        );
    }

    private matchesAdBlockRule(url: string): boolean {
        return this.adBlockRules.some(rule => {
            if (rule.startsWith('||') && rule.endsWith('^')) {
                const domain = rule.slice(2, -1);
                return url.includes(domain);
            }
            return false;
        });
    }

    private spoofReferrer(url: string): string {
        try {
            const urlObj = new URL(url);
            return `${urlObj.protocol}//${urlObj.hostname}/`;
        } catch {
            return 'https://www.google.com/';
        }
    }

    private generateRandomUserAgent(): string {
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0'
        ];

        return userAgents[Math.floor(Math.random() * userAgents.length)];
    }

    private generatePACScript(): string {
        return `
            function FindProxyForURL(url, host) {
                // Use DNS over HTTPS
                if (shExpMatch(host, "*.google.com") || 
                    shExpMatch(host, "*.cloudflare.com") ||
                    shExpMatch(host, "*.quad9.net")) {
                    return "HTTPS *******:443; HTTPS *******:443; DIRECT";
                }
                return "DIRECT";
            }
        `;
    }

    private generateProtectionScript(): string {
        return `
            // Canvas fingerprinting protection
            (function() {
                const originalGetContext = HTMLCanvasElement.prototype.getContext;
                HTMLCanvasElement.prototype.getContext = function(type, ...args) {
                    const context = originalGetContext.apply(this, [type, ...args]);
                    if (type === '2d' || type === 'webgl' || type === 'webgl2') {
                        // Add noise to canvas data
                        const originalToDataURL = this.toDataURL;
                        this.toDataURL = function(...args) {
                            const data = originalToDataURL.apply(this, args);
                            return data + Math.random().toString(36).substr(2, 9);
                        };
                    }
                    return context;
                };
            })();

            // WebRTC leak protection
            if (window.RTCPeerConnection) {
                window.RTCPeerConnection = function() {
                    throw new Error('WebRTC is disabled for privacy');
                };
            }

            // Screen resolution spoofing
            Object.defineProperty(screen, 'width', { value: 1920 });
            Object.defineProperty(screen, 'height', { value: 1080 });
            Object.defineProperty(screen, 'availWidth', { value: 1920 });
            Object.defineProperty(screen, 'availHeight', { value: 1040 });

            // Timezone spoofing
            Date.prototype.getTimezoneOffset = function() {
                return 0; // UTC
            };

            // Language spoofing
            Object.defineProperty(navigator, 'language', { value: 'en-US' });
            Object.defineProperty(navigator, 'languages', { value: ['en-US', 'en'] });
        `;
    }

    private loadSettings(): void {
        const result = this.errorHandler.handleSync(
            () => this.settingsStore.get('privacy'),
            ErrorCode.PRIVACY_SETTINGS_LOAD_FAILED,
            'Failed to load privacy settings from storage'
        );

        if (result.success) {
            this.settings = result.data!;
            secureLogger.info('Privacy settings loaded from storage', 'PrivacyManager');
        } else {
            this.errorHandler.logError(result.error, 'PrivacyManager.loadSettings');
            // Use default settings if loading fails
            this.settings = {
                blockTrackers: true,
                blockAds: true,
                blockFingerprinting: true,
                spoofTimezone: true,
                spoofLanguage: true,
                spoofScreen: true,
                randomizeCanvasFingerprint: true,
                blockWebRTC: true,
                clearCookiesOnExit: true,
                useDoH: false  // Disabled by default due to Electron compatibility issues
            };
        }
    }

    private saveSettings(): ApiResponse<void> {
        return this.errorHandler.handleSync(
            () => this.settingsStore.updatePrivacySettings(this.settings),
            ErrorCode.PRIVACY_SETTINGS_SAVE_FAILED,
            'Failed to save privacy settings to storage'
        );
    }

    updateSettings(newSettings: Partial<PrivacySettings>): ApiResponse<void> {
        try {
            this.errorHandler.validateInput(
                typeof newSettings === 'object' && newSettings !== null,
                'Invalid settings object provided'
            );

            this.settings = { ...this.settings, ...newSettings };
            const saveResult = this.saveSettings();

            if (!saveResult.success) {
                // Revert settings on save failure
                this.loadSettings();
                return saveResult;
            }

            return this.errorHandler.createSuccessResponse(undefined, 'Privacy settings updated successfully');
        } catch (error) {
            this.errorHandler.logError(error, 'PrivacyManager.updateSettings', { newSettings });
            return this.errorHandler.createErrorResponse(error, ErrorCode.PRIVACY_SETTINGS_SAVE_FAILED);
        }
    }

    getSettings(): PrivacySettings {
        return { ...this.settings };
    }

    async clearPrivacyData(): Promise<ApiResponse<void>> {
        return this.errorHandler.handleAsync(
            async () => {
                const ses = session.defaultSession;

                // Clear different types of data with individual error handling
                const clearOperations = [
                    { name: 'storage data', operation: () => ses.clearStorageData() },
                    { name: 'cache', operation: () => ses.clearCache() },
                    { name: 'auth cache', operation: () => ses.clearAuthCache() },
                    { name: 'cookies', operation: () => ses.cookies.flushStore() }
                ];

                const errors: string[] = [];

                for (const { name, operation } of clearOperations) {
                    try {
                        await operation();
                        secureLogger.info(`Successfully cleared ${name}`, 'PrivacyManager');
                    } catch (error) {
                        const errorMsg = `Failed to clear ${name}: ${error instanceof Error ? error.message : String(error)}`;
                        errors.push(errorMsg);
                        secureLogger.error(errorMsg, 'PrivacyManager');
                    }
                }

                if (errors.length > 0) {
                    throw new Error(`Some data could not be cleared: ${errors.join(', ')}`);
                }

                secureLogger.info('All privacy data cleared successfully', 'PrivacyManager');
            },
            ErrorCode.CLEAR_BROWSING_DATA_FAILED,
            'Failed to clear privacy data'
        );
    }
}
