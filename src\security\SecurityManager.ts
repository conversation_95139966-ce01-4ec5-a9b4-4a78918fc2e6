import { session, app, BrowserWindow } from 'electron';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { SettingsStore } from '../utils/SettingsStore';
import { ErrorHandler, ErrorCode, ApiResponse } from '../utils/ErrorHandler';

export interface SecuritySettings {
    enableSandbox: boolean;
    blockDangerousDownloads: boolean;
    enableCSP: boolean;
    blockMixedContent: boolean;
    enableHSTS: boolean;
    blockPlugins: boolean;
    enableMemoryProtection: boolean;
    clearDataOnExit: boolean;
}

export class SecurityManager {
    private settings!: SecuritySettings;
    private encryptionKey: Buffer;
    private secureStorage: Map<string, string>;
    private settingsStore: SettingsStore;
    private errorHandler: ErrorHandler;

    constructor() {
        this.settingsStore = SettingsStore.getInstance();
        this.errorHandler = ErrorHandler.getInstance();
        this.encryptionKey = crypto.randomBytes(32);
        this.secureStorage = new Map();

        // Load settings from persistent storage
        this.loadSettings();
    }

    async initialize(): Promise<void> {
        this.setupSecurityHeaders();
        this.setupDownloadSecurity();
        this.setupContentSecurity();
        this.setupMemoryProtection();
        this.setupProcessIsolation();
    }

    private setupSecurityHeaders(): void {
        const ses = session.defaultSession;

        ses.webRequest.onHeadersReceived({ urls: ['<all_urls>'] }, (details, callback) => {
            const responseHeaders = details.responseHeaders || {};

            if (this.settings.enableCSP) {
                responseHeaders['Content-Security-Policy'] = [
                    "default-src 'self'; " +
                    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                    "style-src 'self' 'unsafe-inline'; " +
                    "img-src 'self' data: https:; " +
                    "connect-src 'self' https:; " +
                    "font-src 'self' data:; " +
                    "object-src 'none'; " +
                    "media-src 'self'; " +
                    "frame-src 'none';"
                ];
            }

            if (this.settings.enableHSTS) {
                responseHeaders['Strict-Transport-Security'] = ['max-age=31536000; includeSubDomains'];
            }

            // Security headers
            responseHeaders['X-Frame-Options'] = ['DENY'];
            responseHeaders['X-Content-Type-Options'] = ['nosniff'];
            responseHeaders['X-XSS-Protection'] = ['1; mode=block'];
            responseHeaders['Referrer-Policy'] = ['strict-origin-when-cross-origin'];
            responseHeaders['Permissions-Policy'] = [
                'camera=(), microphone=(), geolocation=(), payment=(), usb=(), ' +
                'magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), ' +
                'autoplay=(), encrypted-media=(), fullscreen=(), picture-in-picture=()'
            ];

            // Remove server information
            delete responseHeaders['Server'];
            delete responseHeaders['X-Powered-By'];

            callback({ responseHeaders });
        });
    }

    private setupDownloadSecurity(): void {
        const ses = session.defaultSession;

        ses.on('will-download', (event, item, webContents) => {
            if (!this.settings.blockDangerousDownloads) {
                return;
            }

            const filename = item.getFilename();
            const dangerousExtensions = [
                '.exe', '.scr', '.bat', '.cmd', '.com', '.pif', '.vbs', '.js',
                '.jar', '.app', '.deb', '.pkg', '.dmg', '.msi', '.run'
            ];

            const isDangerous = dangerousExtensions.some(ext => 
                filename.toLowerCase().endsWith(ext)
            );

            if (isDangerous) {
                event.preventDefault();
                console.warn(`Blocked dangerous download: ${filename}`);
                return;
            }

            // Scan download for malware (simplified)
            item.on('done', (event, state) => {
                if (state === 'completed') {
                    this.scanFile(item.getSavePath());
                }
            });
        });
    }

    private setupContentSecurity(): void {
        const ses = session.defaultSession;

        // Block mixed content
        if (this.settings.blockMixedContent) {
            ses.webRequest.onBeforeRequest({ urls: ['http://*'] }, (details, callback) => {
                const referer = details.referrer;
                if (referer && referer.startsWith('https://')) {
                    console.warn(`Blocked mixed content: ${details.url}`);
                    callback({ cancel: true });
                    return;
                }
                callback({ cancel: false });
            });
        }

        // Block plugins
        if (this.settings.blockPlugins) {
            ses.setPermissionRequestHandler((webContents, permission, callback) => {
                const blockedPermissions = [
                    'plugins', 'flash', 'java', 'silverlight'
                ];
                callback(!blockedPermissions.includes(permission));
            });
        }

        // Block dangerous protocols
        app.setAsDefaultProtocolClient = () => false;
        
        ses.protocol.interceptHttpProtocol('file', (request, callback) => {
            // Block file:// protocol access
            callback({ error: -3 }); // ERR_ABORTED
        });
    }

    private setupMemoryProtection(): void {
        if (!this.settings.enableMemoryProtection) {
            return;
        }

        // Enable memory protection features
        app.commandLine.appendSwitch('--enable-features', 'VizDisplayCompositor');
        app.commandLine.appendSwitch('--disable-features', 'VizServiceDisplayCompositor');
        app.commandLine.appendSwitch('--enable-heap-profiling');
        app.commandLine.appendSwitch('--max-old-space-size', '512');

        // Monitor memory usage
        setInterval(() => {
            const memoryUsage = process.memoryUsage();
            if (memoryUsage.heapUsed > 500 * 1024 * 1024) { // 500MB
                console.warn('High memory usage detected, triggering garbage collection');
                if (global.gc) {
                    global.gc();
                }
            }
        }, 30000);
    }

    private setupProcessIsolation(): void {
        // Enable site isolation
        app.commandLine.appendSwitch('--site-per-process');
        app.commandLine.appendSwitch('--enable-features', 'SitePerProcess');
        
        // Enable out-of-process iframes
        app.commandLine.appendSwitch('--enable-features', 'OutOfProcessIframes');
        
        // Disable shared array buffer (Spectre mitigation)
        app.commandLine.appendSwitch('--disable-features', 'SharedArrayBuffer');
        
        // Enable strict site isolation
        app.commandLine.appendSwitch('--enable-strict-mixed-content-checking');
    }

    private async scanFile(filePath: string): Promise<boolean> {
        try {
            // Simple file scanning (in production, integrate with antivirus)
            const stats = fs.statSync(filePath);
            
            // Check file size (block extremely large files)
            if (stats.size > 100 * 1024 * 1024) { // 100MB
                console.warn(`Large file detected: ${filePath}`);
                return false;
            }

            // Check file signature
            const buffer = fs.readFileSync(filePath).slice(0, 10);
            const signature = buffer.toString('hex');
            
            // Known malicious signatures (simplified)
            const maliciousSignatures = [
                '4d5a', // PE executable
                '7f454c46', // ELF executable
            ];

            const isMalicious = maliciousSignatures.some(sig => 
                signature.startsWith(sig)
            );

            if (isMalicious) {
                console.warn(`Potentially malicious file: ${filePath}`);
                fs.unlinkSync(filePath); // Delete the file
                return false;
            }

            return true;
        } catch (error) {
            console.error('File scan error:', error);
            return false;
        }
    }

    // Secure storage methods
    encryptData(data: string): string {
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return iv.toString('hex') + ':' + encrypted;
    }

    decryptData(encryptedData: string): string {
        const parts = encryptedData.split(':');
        const iv = Buffer.from(parts[0], 'hex');
        const encrypted = parts[1];
        const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }

    secureStore(key: string, value: string): void {
        const encrypted = this.encryptData(value);
        this.secureStorage.set(key, encrypted);
    }

    secureRetrieve(key: string): string | null {
        const encrypted = this.secureStorage.get(key);
        if (!encrypted) {
            return null;
        }
        try {
            return this.decryptData(encrypted);
        } catch {
            return null;
        }
    }

    secureDelete(key: string): void {
        this.secureStorage.delete(key);
    }

    // Security audit methods
    async performSecurityAudit(): Promise<SecurityAuditResult> {
        const result: SecurityAuditResult = {
            timestamp: new Date(),
            issues: [],
            recommendations: []
        };

        // Check for security issues
        const windows = BrowserWindow.getAllWindows();
        for (const window of windows) {
            const webContents = window.webContents;
            
            if (!webContents.isDevToolsOpened()) {
                result.recommendations.push('Consider enabling dev tools for debugging');
            }

            if (webContents.getURL().startsWith('http://')) {
                result.issues.push('Insecure HTTP connection detected');
            }
        }

        // Check session security
        const ses = session.defaultSession;
        const cookies = await ses.cookies.get({});
        
        for (const cookie of cookies) {
            if (!cookie.secure && cookie.domain !== 'localhost') {
                result.issues.push(`Insecure cookie: ${cookie.name}`);
            }
        }

        return result;
    }

    async clearSecurityData(): Promise<ApiResponse<void>> {
        return this.errorHandler.handleAsync(
            async () => {
                if (this.settings.clearDataOnExit) {
                    const ses = session.defaultSession;

                    // Clear different types of security data with individual error handling
                    const clearOperations = [
                        { name: 'storage data', operation: () => ses.clearStorageData() },
                        { name: 'cache', operation: () => ses.clearCache() },
                        { name: 'auth cache', operation: () => ses.clearAuthCache() },
                        { name: 'secure storage', operation: () => this.secureStorage.clear() }
                    ];

                    const errors: string[] = [];

                    for (const { name, operation } of clearOperations) {
                        try {
                            await operation();
                            console.log(`Successfully cleared ${name}`);
                        } catch (error) {
                            const errorMsg = `Failed to clear ${name}: ${error instanceof Error ? error.message : String(error)}`;
                            errors.push(errorMsg);
                            console.error(errorMsg);
                        }
                    }

                    if (errors.length > 0) {
                        throw new Error(`Some security data could not be cleared: ${errors.join(', ')}`);
                    }

                    console.log('All security data cleared successfully');
                } else {
                    console.log('Security data clearing is disabled in settings');
                }
            },
            ErrorCode.SECURITY_DATA_CLEAR_FAILED,
            'Failed to clear security data'
        );
    }

    private loadSettings(): void {
        const result = this.errorHandler.handleSync(
            () => this.settingsStore.get('security'),
            ErrorCode.SECURITY_SETTINGS_LOAD_FAILED,
            'Failed to load security settings from storage'
        );

        if (result.success) {
            this.settings = result.data!;
            console.log('Security settings loaded from storage');
        } else {
            this.errorHandler.logError(result.error, 'SecurityManager.loadSettings');
            // Use default settings if loading fails
            this.settings = {
                enableSandbox: true,
                blockDangerousDownloads: true,
                enableCSP: true,
                blockMixedContent: true,
                enableHSTS: true,
                blockPlugins: true,
                enableMemoryProtection: true,
                clearDataOnExit: true
            };
        }
    }

    private saveSettings(): ApiResponse<void> {
        return this.errorHandler.handleSync(
            () => this.settingsStore.updateSecuritySettings(this.settings),
            ErrorCode.SECURITY_SETTINGS_SAVE_FAILED,
            'Failed to save security settings to storage'
        );
    }

    updateSettings(newSettings: Partial<SecuritySettings>): ApiResponse<void> {
        try {
            this.errorHandler.validateInput(
                typeof newSettings === 'object' && newSettings !== null,
                'Invalid settings object provided'
            );

            this.settings = { ...this.settings, ...newSettings };
            const saveResult = this.saveSettings();

            if (!saveResult.success) {
                // Revert settings on save failure
                this.loadSettings();
                return saveResult;
            }

            return this.errorHandler.createSuccessResponse(undefined, 'Security settings updated successfully');
        } catch (error) {
            this.errorHandler.logError(error, 'SecurityManager.updateSettings', { newSettings });
            return this.errorHandler.createErrorResponse(error, ErrorCode.SECURITY_SETTINGS_SAVE_FAILED);
        }
    }

    getSettings(): SecuritySettings {
        return { ...this.settings };
    }
}

export interface SecurityAuditResult {
    timestamp: Date;
    issues: string[];
    recommendations: string[];
}
