/**
 * Network Diagnostics Script for Phantom Browser
 * Analyzes network configuration and browsing issues
 */

const { app, session, net } = require('electron');
const dns = require('dns');
const https = require('https');
const http = require('http');

class NetworkDiagnostics {
    constructor() {
        this.results = {
            networkConfig: {},
            proxyStatus: {},
            dohStatus: {},
            connectivity: {},
            sessionInfo: {},
            errors: []
        };
    }

    async runDiagnostics() {
        console.log('🔍 Starting Network Diagnostics for Phantom Browser...\n');
        
        try {
            await this.checkNetworkConfiguration();
            await this.testProxySettings();
            await this.testDoHConfiguration();
            await this.testBasicConnectivity();
            await this.checkElectronSession();
            await this.testWebRequestFiltering();
            
            this.generateDiagnosticReport();
        } catch (error) {
            console.error('❌ Diagnostic failed:', error);
            this.results.errors.push(error.message);
        }
    }

    async checkNetworkConfiguration() {
        console.log('🌐 Checking Network Configuration...');
        
        try {
            // Check system network configuration
            const networkInterfaces = require('os').networkInterfaces();
            const activeInterfaces = Object.keys(networkInterfaces).filter(name => 
                networkInterfaces[name].some(iface => !iface.internal && iface.family === 'IPv4')
            );
            
            this.results.networkConfig = {
                activeInterfaces: activeInterfaces.length,
                hasInternet: await this.testInternetConnectivity(),
                systemDNS: await this.getSystemDNS(),
                timestamp: new Date().toISOString()
            };
            
            console.log(`  ✅ Active network interfaces: ${activeInterfaces.length}`);
            console.log(`  ${this.results.networkConfig.hasInternet ? '✅' : '❌'} Internet connectivity`);
            
        } catch (error) {
            console.log('  ❌ Network configuration check failed:', error.message);
            this.results.errors.push(`Network config: ${error.message}`);
        }
        console.log('');
    }

    async testProxySettings() {
        console.log('🌐 Testing Proxy Configuration...');
        
        try {
            // Test if proxy is configured in Electron session
            const ses = session.defaultSession;
            
            // Check current proxy configuration
            const proxyConfig = await new Promise((resolve, reject) => {
                ses.resolveProxy('https://www.google.com', (proxy) => {
                    resolve(proxy);
                });
            });
            
            this.results.proxyStatus = {
                configured: proxyConfig !== 'DIRECT',
                config: proxyConfig,
                working: false
            };
            
            if (proxyConfig !== 'DIRECT') {
                console.log(`  ⚠️ Proxy detected: ${proxyConfig}`);
                // Test proxy connectivity
                this.results.proxyStatus.working = await this.testProxyConnectivity(proxyConfig);
                console.log(`  ${this.results.proxyStatus.working ? '✅' : '❌'} Proxy connectivity`);
            } else {
                console.log('  ✅ Direct connection (no proxy)');
            }
            
        } catch (error) {
            console.log('  ❌ Proxy test failed:', error.message);
            this.results.errors.push(`Proxy test: ${error.message}`);
        }
        console.log('');
    }

    async testDoHConfiguration() {
        console.log('🔒 Testing DNS over HTTPS Configuration...');
        
        try {
            // Test DoH providers
            const dohProviders = [
                'https://1.1.1.1/dns-query',
                'https://9.9.9.9/dns-query',
                'https://8.8.8.8/dns-query'
            ];
            
            const dohResults = [];
            for (const provider of dohProviders) {
                try {
                    const working = await this.testDoHProvider(provider);
                    dohResults.push({ provider, working });
                    console.log(`  ${working ? '✅' : '❌'} ${provider}`);
                } catch (error) {
                    dohResults.push({ provider, working: false, error: error.message });
                    console.log(`  ❌ ${provider} - ${error.message}`);
                }
            }
            
            this.results.dohStatus = {
                providers: dohResults,
                anyWorking: dohResults.some(r => r.working)
            };
            
        } catch (error) {
            console.log('  ❌ DoH test failed:', error.message);
            this.results.errors.push(`DoH test: ${error.message}`);
        }
        console.log('');
    }

    async testBasicConnectivity() {
        console.log('🔗 Testing Basic Connectivity...');
        
        const testSites = [
            'https://www.google.com',
            'https://www.cloudflare.com',
            'https://httpbin.org/get',
            'http://example.com'
        ];
        
        const connectivityResults = [];
        
        for (const site of testSites) {
            try {
                const result = await this.testSiteConnectivity(site);
                connectivityResults.push({ site, ...result });
                console.log(`  ${result.success ? '✅' : '❌'} ${site} (${result.time}ms)`);
            } catch (error) {
                connectivityResults.push({ site, success: false, error: error.message });
                console.log(`  ❌ ${site} - ${error.message}`);
            }
        }
        
        this.results.connectivity = {
            tests: connectivityResults,
            successRate: connectivityResults.filter(r => r.success).length / connectivityResults.length
        };
        
        console.log('');
    }

    async checkElectronSession() {
        console.log('⚡ Checking Electron Session Configuration...');
        
        try {
            const ses = session.defaultSession;
            
            // Check session configuration
            const sessionInfo = {
                userAgent: ses.getUserAgent(),
                canUseWebRTC: true, // Default assumption
                cookiesEnabled: true,
                cacheEnabled: true,
                webSecurityEnabled: true
            };
            
            // Check if web requests are being blocked
            const webRequestFilters = await this.checkWebRequestFilters(ses);
            
            this.results.sessionInfo = {
                ...sessionInfo,
                webRequestFilters,
                timestamp: new Date().toISOString()
            };
            
            console.log(`  ✅ User Agent: ${sessionInfo.userAgent.substring(0, 50)}...`);
            console.log(`  ${webRequestFilters.hasFilters ? '⚠️' : '✅'} Web request filters: ${webRequestFilters.count}`);
            
        } catch (error) {
            console.log('  ❌ Session check failed:', error.message);
            this.results.errors.push(`Session check: ${error.message}`);
        }
        console.log('');
    }

    async testWebRequestFiltering() {
        console.log('🛡️ Testing Web Request Filtering...');
        
        try {
            // Test if requests are being blocked by privacy features
            const testRequest = await this.makeTestRequest('https://httpbin.org/get');
            
            console.log(`  ${testRequest.success ? '✅' : '❌'} Test HTTP request`);
            if (!testRequest.success) {
                console.log(`    Error: ${testRequest.error}`);
            }
            
        } catch (error) {
            console.log('  ❌ Web request test failed:', error.message);
            this.results.errors.push(`Web request test: ${error.message}`);
        }
        console.log('');
    }

    // Helper methods
    async testInternetConnectivity() {
        return new Promise((resolve) => {
            const req = http.get('http://www.google.com', (res) => {
                resolve(res.statusCode === 200);
            });
            req.on('error', () => resolve(false));
            req.setTimeout(5000, () => {
                req.destroy();
                resolve(false);
            });
        });
    }

    async getSystemDNS() {
        return new Promise((resolve) => {
            dns.getServers((servers) => {
                resolve(servers);
            });
        });
    }

    async testProxyConnectivity(proxyConfig) {
        // Simple proxy connectivity test
        return new Promise((resolve) => {
            // This is a simplified test - in reality, we'd need to parse the proxy config
            // and test the actual proxy server
            resolve(true); // Assume working for now
        });
    }

    async testDoHProvider(provider) {
        return new Promise((resolve) => {
            const req = https.get(provider, (res) => {
                resolve(res.statusCode === 200 || res.statusCode === 400); // 400 is OK for DoH without query
            });
            req.on('error', () => resolve(false));
            req.setTimeout(5000, () => {
                req.destroy();
                resolve(false);
            });
        });
    }

    async testSiteConnectivity(url) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            const protocol = url.startsWith('https:') ? https : http;
            
            const req = protocol.get(url, (res) => {
                const time = Date.now() - startTime;
                resolve({
                    success: res.statusCode >= 200 && res.statusCode < 400,
                    statusCode: res.statusCode,
                    time
                });
            });
            
            req.on('error', (error) => {
                const time = Date.now() - startTime;
                resolve({
                    success: false,
                    error: error.message,
                    time
                });
            });
            
            req.setTimeout(10000, () => {
                req.destroy();
                resolve({
                    success: false,
                    error: 'Timeout',
                    time: 10000
                });
            });
        });
    }

    async checkWebRequestFilters(session) {
        // Check if there are web request filters that might be blocking requests
        return {
            hasFilters: false, // This would need actual session inspection
            count: 0,
            types: []
        };
    }

    async makeTestRequest(url) {
        return new Promise((resolve) => {
            const req = https.get(url, (res) => {
                resolve({
                    success: res.statusCode === 200,
                    statusCode: res.statusCode
                });
            });
            req.on('error', (error) => {
                resolve({
                    success: false,
                    error: error.message
                });
            });
            req.setTimeout(5000, () => {
                req.destroy();
                resolve({
                    success: false,
                    error: 'Timeout'
                });
            });
        });
    }

    generateDiagnosticReport() {
        console.log('📊 NETWORK DIAGNOSTIC REPORT');
        console.log('==============================\n');
        
        // Network Configuration Summary
        console.log('🌐 NETWORK CONFIGURATION:');
        console.log(`  Internet Connectivity: ${this.results.networkConfig.hasInternet ? '✅ Working' : '❌ Failed'}`);
        console.log(`  Active Interfaces: ${this.results.networkConfig.activeInterfaces}`);
        console.log(`  System DNS: ${this.results.networkConfig.systemDNS?.join(', ') || 'Unknown'}\n`);
        
        // Proxy Status
        console.log('🌐 PROXY STATUS:');
        if (this.results.proxyStatus.configured) {
            console.log(`  Configuration: ${this.results.proxyStatus.config}`);
            console.log(`  Working: ${this.results.proxyStatus.working ? '✅ Yes' : '❌ No'}`);
        } else {
            console.log('  Status: ✅ Direct connection (no proxy)');
        }
        console.log('');
        
        // DoH Status
        console.log('🔒 DNS OVER HTTPS STATUS:');
        console.log(`  Any Provider Working: ${this.results.dohStatus.anyWorking ? '✅ Yes' : '❌ No'}`);
        this.results.dohStatus.providers?.forEach(provider => {
            console.log(`  ${provider.working ? '✅' : '❌'} ${provider.provider}`);
        });
        console.log('');
        
        // Connectivity Results
        console.log('🔗 CONNECTIVITY TEST RESULTS:');
        console.log(`  Success Rate: ${(this.results.connectivity.successRate * 100).toFixed(1)}%`);
        this.results.connectivity.tests?.forEach(test => {
            console.log(`  ${test.success ? '✅' : '❌'} ${test.site} ${test.time ? `(${test.time}ms)` : ''}`);
        });
        console.log('');
        
        // Errors
        if (this.results.errors.length > 0) {
            console.log('❌ ERRORS DETECTED:');
            this.results.errors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error}`);
            });
            console.log('');
        }
        
        // Recommendations
        this.generateRecommendations();
    }

    generateRecommendations() {
        console.log('💡 RECOMMENDATIONS:');
        
        if (!this.results.networkConfig.hasInternet) {
            console.log('  🔴 CRITICAL: No internet connectivity detected');
            console.log('    - Check your network connection');
            console.log('    - Verify network adapter settings');
            console.log('    - Check firewall/antivirus settings');
        }
        
        if (this.results.proxyStatus.configured && !this.results.proxyStatus.working) {
            console.log('  🟡 WARNING: Proxy configured but not working');
            console.log('    - Verify proxy server settings');
            console.log('    - Check proxy authentication');
            console.log('    - Try switching to direct connection');
        }
        
        if (!this.results.dohStatus.anyWorking) {
            console.log('  🟡 WARNING: DoH providers not accessible');
            console.log('    - Check firewall settings for HTTPS traffic');
            console.log('    - Try switching to direct DNS');
        }
        
        if (this.results.connectivity.successRate < 0.5) {
            console.log('  🔴 CRITICAL: Poor connectivity to test sites');
            console.log('    - Check if privacy features are too restrictive');
            console.log('    - Verify Electron session configuration');
            console.log('    - Check for web request blocking');
        }
        
        if (this.results.errors.length === 0 && this.results.connectivity.successRate > 0.8) {
            console.log('  ✅ Network configuration appears healthy');
            console.log('    - Issue may be with specific website or browser component');
            console.log('    - Check browser console for JavaScript errors');
        }
    }
}

// Run diagnostics
const diagnostics = new NetworkDiagnostics();
diagnostics.runDiagnostics().catch(console.error);
