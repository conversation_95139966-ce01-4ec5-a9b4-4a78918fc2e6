# Phantom Browser Troubleshooting Guide

## 🚨 Common Issues and Solutions

### 🗑️ Clear Browsing Data Issues

#### Issue: "Clear Browsing Data" Button Not Responding
**Symptoms:**
- Button click has no effect
- No confirmation dialog appears
- <PERSON><PERSON> appears disabled

**Solutions:**
1. **Refresh the page** and try again
2. **Check browser console** (F12) for JavaScript errors
3. **Restart the application** completely
4. **Clear browser cache** and reload

**Advanced Debugging:**
```javascript
// Open browser console (F12) and run:
window.phantomAPI.clearBrowsingData().then(console.log).catch(console.error);
```

#### Issue: Data Clearing Fails with Error
**Symptoms:**
- Red error notification appears
- "Failed to clear browsing data" message
- Partial data clearing

**Solutions:**
1. **Close other applications** that might be using browser files
2. **Run as administrator** to ensure file permissions
3. **Check disk space** - ensure sufficient free space
4. **Restart and try again** after closing all browser instances

**Error Code Reference:**
- `STORAGE_WRITE_FAILED`: Permission or disk space issue
- `CLEAR_BROWSING_DATA_FAILED`: General clearing failure
- `PERMISSION_DENIED`: Insufficient file permissions

---

### 🌐 Proxy Management Issues

#### Issue: Proxy Connection Fails
**Symptoms:**
- Red status indicator
- "Proxy connection failed" error
- Unable to browse websites

**Diagnostic Steps:**
1. **Test proxy manually:**
   ```bash
   curl -x http://proxy.example.com:8080 http://httpbin.org/ip
   ```
2. **Check proxy server status** with provider
3. **Verify proxy credentials** if authentication required
4. **Test different proxy servers**

**Solutions:**
- **Update proxy settings** with correct host/port
- **Check firewall settings** - ensure proxy ports aren't blocked
- **Try different proxy type** (HTTP vs SOCKS5)
- **Contact proxy provider** for server status

#### Issue: Proxy Validation Errors
**Symptoms:**
- "Invalid proxy configuration" message
- Cannot add new proxy
- Validation fails immediately

**Common Validation Errors:**
- **Invalid host format**: Use proper hostname or IP address
- **Port out of range**: Use ports 1-65535
- **Missing credentials**: Provide username/password if required
- **Duplicate proxy**: Proxy already exists in list

**Solutions:**
```javascript
// Test proxy configuration format:
const testProxy = {
    type: 'http',           // or 'https', 'socks4', 'socks5'
    host: 'proxy.example.com',
    port: 8080,
    username: 'user',       // optional
    password: 'pass',       // optional
    enabled: true
};
```

#### Issue: Proxy Authentication Fails
**Symptoms:**
- "Proxy authentication failed" error
- 407 Proxy Authentication Required
- Connection established but no data transfer

**Solutions:**
1. **Verify credentials** with proxy provider
2. **Check username/password format** - no special characters
3. **Try without authentication** first to test basic connectivity
4. **Update proxy configuration** with correct credentials

---

### 🔒 DNS over HTTPS (DoH) Issues

#### Issue: DoH Not Working
**Symptoms:**
- DNS queries not encrypted
- Websites load slowly or fail
- Network status shows error

**Diagnostic Steps:**
1. **Test DoH provider manually:**
   ```bash
   nslookup google.com *******
   ```
2. **Check firewall settings** for HTTPS traffic
3. **Try different DoH provider**
4. **Verify internet connectivity**

**Solutions:**
- **Switch DoH provider**: Try Cloudflare, Quad9, or Google
- **Check network configuration**: Ensure no conflicts with proxy
- **Disable temporarily**: Switch to direct connection for testing
- **Update DNS settings**: Clear system DNS cache

#### Issue: DoH and Proxy Conflicts
**Symptoms:**
- Inconsistent connection behavior
- Some sites work, others don't
- Network status shows conflicting information

**Understanding Priority:**
1. **Proxy** (highest priority) - overrides DoH
2. **DoH** (medium priority) - used when no proxy
3. **Direct** (lowest priority) - fallback option

**Solutions:**
- **Choose one method**: Use either proxy OR DoH, not both
- **Check network status**: Verify which mode is actually active
- **Reset configuration**: Switch to direct, then reconfigure

---

### 📊 Network Status Issues

#### Issue: Status Indicator Not Updating
**Symptoms:**
- Status shows outdated information
- Indicator doesn't change color
- "Last updated" timestamp is old

**Solutions:**
1. **Manual refresh**: Click "Refresh Status" button
2. **Check API connectivity**: Ensure phantomAPI is available
3. **Restart application**: Force complete status refresh
4. **Clear browser cache**: Remove cached status data

#### Issue: Incorrect Status Display
**Symptoms:**
- Status shows "Direct" but proxy is configured
- Green indicator but connection not working
- Conflicting information in status details

**Debugging Steps:**
```javascript
// Check actual network configuration:
window.phantomAPI.getNetworkConfig().then(console.log);
window.phantomAPI.getNetworkStatus().then(console.log);
```

**Solutions:**
- **Verify actual configuration**: Check what's really active
- **Reset network settings**: Clear and reconfigure
- **Update status manually**: Force refresh from backend

---

### 💾 Settings Persistence Issues

#### Issue: Settings Not Saving
**Symptoms:**
- Settings reset after restart
- "Failed to save settings" error
- Changes don't persist

**Common Causes:**
- **Insufficient permissions**: Can't write to settings file
- **Disk space full**: No room for settings storage
- **File corruption**: Settings file is damaged
- **Concurrent access**: Multiple instances accessing same file

**Solutions:**
1. **Run as administrator**: Ensure write permissions
2. **Check disk space**: Free up storage if needed
3. **Reset settings storage**: Delete and recreate settings file
4. **Close other instances**: Ensure only one browser running

#### Issue: Settings File Corruption
**Symptoms:**
- Application crashes on startup
- "Settings corrupted" error
- Default settings always loaded

**Recovery Steps:**
1. **Locate settings file**: Usually in user data directory
2. **Backup corrupted file**: For analysis if needed
3. **Delete settings file**: Force recreation with defaults
4. **Restart application**: Should create new settings file
5. **Reconfigure manually**: Set up preferences again

---

### ⚡ Performance Issues

#### Issue: Slow Application Startup
**Symptoms:**
- Takes >10 seconds to start
- Hangs during initialization
- High CPU usage during startup

**Optimization Steps:**
1. **Clear browsing data**: Remove accumulated data
2. **Reduce proxy list**: Limit to 10-15 active proxies
3. **Check system resources**: Ensure sufficient RAM/CPU
4. **Update application**: Install latest version

#### Issue: High Memory Usage
**Symptoms:**
- RAM usage >500MB
- System becomes sluggish
- Browser crashes or freezes

**Memory Optimization:**
1. **Clear data regularly**: Weekly clearing recommended
2. **Limit proxy configurations**: Remove unused proxies
3. **Restart periodically**: Fresh memory allocation
4. **Monitor background processes**: Check for memory leaks

---

### 🔧 Advanced Troubleshooting

#### Debug Console Commands
```javascript
// Test all API functions:
window.phantomAPI.getPrivacySettings().then(console.log);
window.phantomAPI.getProxySettings().then(console.log);
window.phantomAPI.getNetworkStatus().then(console.log);

// Force settings refresh:
window.location.reload();

// Test proxy connectivity:
window.phantomAPI.testProxy({
    type: 'http',
    host: 'proxy.example.com',
    port: 8080
}).then(console.log);
```

#### Log File Analysis
**Location**: Check browser console (F12) → Console tab
**Key Error Patterns:**
- `Failed to load privacy settings`: Settings persistence issue
- `Proxy connection failed`: Network connectivity problem
- `Invalid proxy PAC url`: DoH configuration error
- `Permission denied`: File access rights issue

#### System Requirements Check
- **RAM**: Minimum 4GB, recommended 8GB+
- **Disk Space**: 500MB free for settings and cache
- **Network**: Stable internet connection
- **Permissions**: Write access to user data directory

---

### 📞 Getting Additional Help

#### Before Contacting Support
1. **Check this troubleshooting guide** thoroughly
2. **Collect error messages** and screenshots
3. **Note system specifications** and OS version
4. **Try basic solutions** (restart, clear data, etc.)
5. **Document steps to reproduce** the issue

#### Information to Provide
- **Operating System**: Windows/Mac/Linux version
- **Browser Version**: Check About section
- **Error Messages**: Exact text of any errors
- **Network Configuration**: Proxy/DoH settings
- **Steps to Reproduce**: Detailed sequence of actions
- **Console Logs**: Copy from F12 developer tools

#### Emergency Recovery
If the application becomes completely unusable:
1. **Close all browser instances**
2. **Delete settings directory** (backup first)
3. **Restart application** (will use defaults)
4. **Reconfigure step by step**
5. **Test each feature individually**

---

*This troubleshooting guide covers the most common issues. For additional help, check the application logs and community forums.*
