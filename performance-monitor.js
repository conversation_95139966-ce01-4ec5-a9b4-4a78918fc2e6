/**
 * Performance Monitoring Script for Phantom Browser Settings Operations
 * Measures timing, memory usage, and performance impact of privacy features
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

class PerformanceMonitor {
    constructor() {
        this.results = {
            settingsOperations: [],
            memoryUsage: [],
            startupTimes: [],
            largeProxyListTests: []
        };
        this.baselineMemory = process.memoryUsage();
    }

    async runPerformanceTests() {
        console.log('⚡ Starting Performance Monitoring Tests...\n');
        
        await this.testSettingsOperations();
        await this.testMemoryUsage();
        await this.testLargeProxyLists();
        await this.measureStartupImpact();
        
        this.generatePerformanceReport();
    }

    async testSettingsOperations() {
        console.log('📊 Testing Settings Operations Performance...');
        
        const operations = [
            { name: 'Privacy Settings Save', operation: () => this.simulatePrivacySettingsSave() },
            { name: 'Privacy Settings Load', operation: () => this.simulatePrivacySettingsLoad() },
            { name: 'Security Settings Save', operation: () => this.simulateSecuritySettingsSave() },
            { name: 'Security Settings Load', operation: () => this.simulateSecuritySettingsLoad() },
            { name: 'Proxy Settings Save', operation: () => this.simulateProxySettingsSave() },
            { name: 'Proxy Settings Load', operation: () => this.simulateProxySettingsLoad() }
        ];

        for (const op of operations) {
            const times = [];
            
            // Run each operation 10 times to get average
            for (let i = 0; i < 10; i++) {
                const startTime = performance.now();
                await op.operation();
                const endTime = performance.now();
                times.push(endTime - startTime);
            }
            
            const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
            const minTime = Math.min(...times);
            const maxTime = Math.max(...times);
            
            this.results.settingsOperations.push({
                operation: op.name,
                averageTime: avgTime,
                minTime,
                maxTime,
                samples: times.length
            });
            
            console.log(`  ✅ ${op.name}: ${avgTime.toFixed(2)}ms avg (${minTime.toFixed(2)}-${maxTime.toFixed(2)}ms)`);
        }
        console.log('');
    }

    async testMemoryUsage() {
        console.log('🧠 Testing Memory Usage During Settings Operations...');
        
        const initialMemory = process.memoryUsage();
        console.log(`  📊 Initial Memory: ${this.formatMemory(initialMemory.heapUsed)}`);
        
        // Simulate loading large settings
        const startTime = performance.now();
        await this.simulateLargeSettingsLoad();
        const loadTime = performance.now() - startTime;
        
        const afterLoadMemory = process.memoryUsage();
        console.log(`  📊 After Large Settings Load: ${this.formatMemory(afterLoadMemory.heapUsed)} (+${this.formatMemory(afterLoadMemory.heapUsed - initialMemory.heapUsed)})`);
        
        // Simulate multiple save operations
        for (let i = 0; i < 50; i++) {
            await this.simulatePrivacySettingsSave();
            await this.simulateProxySettingsSave();
        }
        
        const afterOperationsMemory = process.memoryUsage();
        console.log(`  📊 After 100 Save Operations: ${this.formatMemory(afterOperationsMemory.heapUsed)} (+${this.formatMemory(afterOperationsMemory.heapUsed - initialMemory.heapUsed)})`);
        
        this.results.memoryUsage.push({
            initial: initialMemory.heapUsed,
            afterLoad: afterLoadMemory.heapUsed,
            afterOperations: afterOperationsMemory.heapUsed,
            loadTime
        });
        
        console.log('');
    }

    async testLargeProxyLists() {
        console.log('🌐 Testing Performance with Large Proxy Lists...');
        
        const proxySizes = [10, 25, 50, 100];
        
        for (const size of proxySizes) {
            const proxyList = this.generateLargeProxyList(size);
            
            const startTime = performance.now();
            await this.simulateProxyListSave(proxyList);
            const saveTime = performance.now() - startTime;
            
            const loadStartTime = performance.now();
            await this.simulateProxyListLoad(proxyList);
            const loadTime = performance.now() - loadStartTime;
            
            this.results.largeProxyListTests.push({
                proxyCount: size,
                saveTime,
                loadTime,
                totalTime: saveTime + loadTime
            });
            
            console.log(`  ✅ ${size} proxies: Save ${saveTime.toFixed(2)}ms, Load ${loadTime.toFixed(2)}ms`);
        }
        console.log('');
    }

    async measureStartupImpact() {
        console.log('🚀 Measuring Startup Time Impact...');
        
        // Simulate startup with different settings configurations
        const configurations = [
            { name: 'Minimal Settings', settingsSize: 'small' },
            { name: 'Standard Settings', settingsSize: 'medium' },
            { name: 'Maximum Settings', settingsSize: 'large' }
        ];
        
        for (const config of configurations) {
            const times = [];
            
            // Simulate startup 5 times for each configuration
            for (let i = 0; i < 5; i++) {
                const startTime = performance.now();
                await this.simulateApplicationStartup(config.settingsSize);
                const endTime = performance.now();
                times.push(endTime - startTime);
            }
            
            const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
            
            this.results.startupTimes.push({
                configuration: config.name,
                averageTime: avgTime,
                samples: times
            });
            
            console.log(`  ✅ ${config.name}: ${avgTime.toFixed(2)}ms avg startup time`);
        }
        console.log('');
    }

    // Simulation methods
    async simulatePrivacySettingsSave() {
        // Simulate JSON serialization and file write
        const settings = {
            blockTrackers: true,
            blockAds: true,
            blockFingerprinting: true,
            spoofTimezone: true,
            spoofLanguage: true,
            spoofScreen: true,
            randomizeCanvasFingerprint: true,
            blockWebRTC: true,
            clearCookiesOnExit: true,
            useDoH: true
        };
        
        const serialized = JSON.stringify(settings);
        // Simulate file I/O delay
        await this.simulateDelay(1, 3);
        return serialized.length;
    }

    async simulatePrivacySettingsLoad() {
        // Simulate file read and JSON parsing
        await this.simulateDelay(0.5, 2);
        return { loaded: true };
    }

    async simulateSecuritySettingsSave() {
        const settings = {
            enableSandbox: true,
            blockDangerousDownloads: true,
            enableCSP: true,
            blockMixedContent: true,
            enableHSTS: true,
            blockPlugins: true,
            enableMemoryProtection: true,
            clearDataOnExit: true
        };
        
        const serialized = JSON.stringify(settings);
        await this.simulateDelay(1, 3);
        return serialized.length;
    }

    async simulateSecuritySettingsLoad() {
        await this.simulateDelay(0.5, 2);
        return { loaded: true };
    }

    async simulateProxySettingsSave() {
        const settings = {
            currentProxy: null,
            proxyList: [],
            rotationEnabled: false
        };
        
        const serialized = JSON.stringify(settings);
        await this.simulateDelay(1, 3);
        return serialized.length;
    }

    async simulateProxySettingsLoad() {
        await this.simulateDelay(0.5, 2);
        return { loaded: true };
    }

    async simulateLargeSettingsLoad() {
        // Simulate loading all settings at once
        await Promise.all([
            this.simulatePrivacySettingsLoad(),
            this.simulateSecuritySettingsLoad(),
            this.simulateProxySettingsLoad()
        ]);
        await this.simulateDelay(5, 10); // Additional processing time
    }

    generateLargeProxyList(size) {
        const proxies = [];
        for (let i = 0; i < size; i++) {
            proxies.push({
                type: i % 2 === 0 ? 'http' : 'socks5',
                host: `proxy${i}.example.com`,
                port: 8080 + i,
                enabled: i % 3 === 0,
                name: `Proxy ${i}`,
                description: `Test proxy number ${i}`
            });
        }
        return proxies;
    }

    async simulateProxyListSave(proxyList) {
        const serialized = JSON.stringify(proxyList);
        // Larger lists take longer to serialize and save
        const delay = Math.max(1, proxyList.length * 0.1);
        await this.simulateDelay(delay, delay + 2);
        return serialized.length;
    }

    async simulateProxyListLoad(proxyList) {
        // Simulate parsing and validation
        const delay = Math.max(0.5, proxyList.length * 0.05);
        await this.simulateDelay(delay, delay + 1);
        return proxyList.length;
    }

    async simulateApplicationStartup(settingsSize) {
        const delays = {
            small: { min: 10, max: 20 },
            medium: { min: 15, max: 30 },
            large: { min: 25, max: 50 }
        };
        
        const delay = delays[settingsSize] || delays.medium;
        await this.simulateDelay(delay.min, delay.max);
    }

    async simulateDelay(minMs, maxMs) {
        const delay = Math.random() * (maxMs - minMs) + minMs;
        return new Promise(resolve => setTimeout(resolve, delay));
    }

    formatMemory(bytes) {
        const mb = bytes / 1024 / 1024;
        return `${mb.toFixed(2)} MB`;
    }

    generatePerformanceReport() {
        console.log('📈 PERFORMANCE MONITORING REPORT');
        console.log('==================================\n');
        
        // Settings Operations Performance
        console.log('⚡ Settings Operations Performance:');
        this.results.settingsOperations.forEach(op => {
            const status = op.averageTime < 5 ? '🟢' : op.averageTime < 10 ? '🟡' : '🔴';
            console.log(`  ${status} ${op.operation}: ${op.averageTime.toFixed(2)}ms avg`);
        });
        console.log('');
        
        // Memory Usage Analysis
        console.log('🧠 Memory Usage Analysis:');
        this.results.memoryUsage.forEach(mem => {
            const loadIncrease = mem.afterLoad - mem.initial;
            const operationsIncrease = mem.afterOperations - mem.afterLoad;
            console.log(`  📊 Settings Load Impact: +${this.formatMemory(loadIncrease)}`);
            console.log(`  📊 Operations Impact: +${this.formatMemory(operationsIncrease)}`);
            console.log(`  📊 Total Memory Increase: +${this.formatMemory(mem.afterOperations - mem.initial)}`);
        });
        console.log('');
        
        // Large Proxy Lists Performance
        console.log('🌐 Large Proxy Lists Performance:');
        this.results.largeProxyListTests.forEach(test => {
            const status = test.totalTime < 50 ? '🟢' : test.totalTime < 100 ? '🟡' : '🔴';
            console.log(`  ${status} ${test.proxyCount} proxies: ${test.totalTime.toFixed(2)}ms total`);
        });
        console.log('');
        
        // Startup Time Impact
        console.log('🚀 Startup Time Impact:');
        this.results.startupTimes.forEach(startup => {
            const status = startup.averageTime < 30 ? '🟢' : startup.averageTime < 60 ? '🟡' : '🔴';
            console.log(`  ${status} ${startup.configuration}: ${startup.averageTime.toFixed(2)}ms`);
        });
        console.log('');
        
        // Performance Summary
        const avgSettingsTime = this.results.settingsOperations.reduce((sum, op) => sum + op.averageTime, 0) / this.results.settingsOperations.length;
        const maxProxyTime = Math.max(...this.results.largeProxyListTests.map(t => t.totalTime));
        const maxStartupTime = Math.max(...this.results.startupTimes.map(s => s.averageTime));
        
        console.log('📋 PERFORMANCE SUMMARY:');
        console.log(`  Average Settings Operation: ${avgSettingsTime.toFixed(2)}ms`);
        console.log(`  Max Proxy List Operation: ${maxProxyTime.toFixed(2)}ms`);
        console.log(`  Max Startup Time: ${maxStartupTime.toFixed(2)}ms`);
        
        if (avgSettingsTime < 10 && maxProxyTime < 100 && maxStartupTime < 60) {
            console.log('  🎉 EXCELLENT PERFORMANCE - All operations within acceptable limits!');
        } else if (avgSettingsTime < 20 && maxProxyTime < 200 && maxStartupTime < 120) {
            console.log('  ✅ GOOD PERFORMANCE - Minor optimizations may be beneficial.');
        } else {
            console.log('  ⚠️ PERFORMANCE CONCERNS - Consider optimization for better user experience.');
        }
    }
}

// Run performance monitoring
const monitor = new PerformanceMonitor();
monitor.runPerformanceTests().catch(console.error);
