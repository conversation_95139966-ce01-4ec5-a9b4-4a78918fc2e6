{"version": 3, "file": "PrivacyManager.js", "sourceRoot": "", "sources": ["../../src/privacy/PrivacyManager.ts"], "names": [], "mappings": ";;;AAAA,uCAAgD;AAEhD,0DAAuD;AACvD,wDAA6E;AAe7E,MAAa,cAAc;IAOvB;QACI,IAAI,CAAC,aAAa,GAAG,6BAAa,CAAC,WAAW,EAAE,CAAC;QACjD,IAAI,CAAC,YAAY,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC;QAC/C,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QAEvB,wCAAwC;QACxC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5B,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,wBAAwB;QACxB,MAAM,eAAe,GAAG;YACpB,sBAAsB;YACtB,sBAAsB;YACtB,cAAc;YACd,iBAAiB;YACjB,uBAAuB;YACvB,qBAAqB;YACrB,qBAAqB;YACrB,uBAAuB;YACvB,gBAAgB;YAChB,cAAc;YACd,aAAa;YACb,aAAa;YACb,eAAe;YACf,eAAe;YACf,YAAY;YACZ,eAAe;YACf,eAAe;YACf,cAAc;YACd,cAAc;YACd,aAAa;YACb,eAAe;SAClB,CAAC;QAEF,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAEpE,mDAAmD;QACnD,IAAI,CAAC,YAAY,GAAG;YAChB,yBAAyB;YACzB,0BAA0B;YAC1B,oBAAoB;YACpB,wBAAwB;YACxB,oBAAoB;YACpB,yBAAyB;YACzB,yBAAyB;YACzB,yBAAyB;SAC5B,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC1B,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,2BAA2B;QAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvB,GAAG,CAAC,QAAQ,CAAC;gBACT,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE;aACtC,CAAC,CAAC;QACP,CAAC;QAED,4BAA4B;QAC5B,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;YACZ,GAAG,EAAE,qBAAqB;YAC1B,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,QAAQ;SAClB,CAAC,CAAC;QAEH,uBAAuB;QACvB,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;IACrD,CAAC;IAEO,wBAAwB;QAC5B,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,0BAA0B;QAC1B,GAAG,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC3E,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAEjC,sCAAsC;YACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrE,QAAQ,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC3B,OAAO;YACX,CAAC;YAED,uBAAuB;YACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjE,QAAQ,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC3B,OAAO;YACX,CAAC;YAED,QAAQ,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,GAAG,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC/E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC;YAEvC,0BAA0B;YAC1B,OAAO,OAAO,CAAC,kBAAkB,CAAC,CAAC;YACnC,OAAO,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAClC,OAAO,OAAO,CAAC,WAAW,CAAC,CAAC;YAE5B,iBAAiB;YACjB,IAAI,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACzD,CAAC;YAED,sBAAsB;YACtB,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;YACrB,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;YAEzB,QAAQ,CAAC,EAAE,cAAc,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB;QACxB,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,oCAAoC;QACpC,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC7E,IAAI,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACjG,0CAA0C;gBAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAC/C,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC3D,CAAC;YAED,QAAQ,CAAC,EAAE,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACrC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAClD,QAAQ,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,GAAG,MAAM,CAAC,CACzD,CAAC;IACN,CAAC;IAEO,kBAAkB,CAAC,GAAW;QAClC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACjC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjC,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChC,CAAC;YACD,OAAO,KAAK,CAAC;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,GAAW;QAC7B,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC;QACrD,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,yBAAyB,CAAC;QACrC,CAAC;IACL,CAAC;IAEO,uBAAuB;QAC3B,MAAM,UAAU,GAAG;YACf,iHAAiH;YACjH,uHAAuH;YACvH,uGAAuG;YACvG,kFAAkF;YAClF,sFAAsF;SACzF,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;IACrE,CAAC;IAEO,iBAAiB;QACrB,OAAO;;;;;;;;;;SAUN,CAAC;IACN,CAAC;IAEO,wBAAwB;QAC5B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAuCN,CAAC;IACN,CAAC;IAEO,YAAY;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CACvC,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EACvC,wBAAS,CAAC,4BAA4B,EACtC,8CAA8C,CACjD,CAAC;QAEF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAK,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,6BAA6B,CAAC,CAAC;YACxE,wCAAwC;YACxC,IAAI,CAAC,QAAQ,GAAG;gBACZ,aAAa,EAAE,IAAI;gBACnB,QAAQ,EAAE,IAAI;gBACd,mBAAmB,EAAE,IAAI;gBACzB,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,IAAI;gBACjB,0BAA0B,EAAE,IAAI;gBAChC,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,MAAM,EAAE,IAAI;aACf,CAAC;QACN,CAAC;IACL,CAAC;IAEO,YAAY;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAC/B,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAC7D,wBAAS,CAAC,4BAA4B,EACtC,4CAA4C,CAC/C,CAAC;IACN,CAAC;IAED,cAAc,CAAC,WAAqC;QAChD,IAAI,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,aAAa,CAC3B,OAAO,WAAW,KAAK,QAAQ,IAAI,WAAW,KAAK,IAAI,EACvD,kCAAkC,CACrC,CAAC;YAEF,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,EAAE,CAAC;YACrD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAEvC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACtB,kCAAkC;gBAClC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,OAAO,UAAU,CAAC;YACtB,CAAC;YAED,OAAO,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,SAAS,EAAE,uCAAuC,CAAC,CAAC;QACvG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,EAAE,+BAA+B,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;YACpF,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,KAAK,EAAE,wBAAS,CAAC,4BAA4B,CAAC,CAAC;QAChG,CAAC;IACL,CAAC;IAED,WAAW;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,gBAAgB;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAChC,KAAK,IAAI,EAAE;YACP,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;YAEnC,+DAA+D;YAC/D,MAAM,eAAe,GAAG;gBACpB,EAAE,IAAI,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE;gBACjE,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE;gBACpD,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE;gBAC7D,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;aACjE,CAAC;YAEF,MAAM,MAAM,GAAa,EAAE,CAAC;YAE5B,KAAK,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,eAAe,EAAE,CAAC;gBAChD,IAAI,CAAC;oBACD,MAAM,SAAS,EAAE,CAAC;oBAClB,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,EAAE,CAAC,CAAC;gBAChD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,MAAM,QAAQ,GAAG,mBAAmB,IAAI,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;oBACtG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACtB,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAC5B,CAAC;YACL,CAAC;YAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,mCAAmC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5E,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACzD,CAAC,EACD,wBAAS,CAAC,0BAA0B,EACpC,8BAA8B,CACjC,CAAC;IACN,CAAC;CACJ;AAnVD,wCAmVC"}