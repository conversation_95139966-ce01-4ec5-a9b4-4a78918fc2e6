{"name": "phantom-browser", "version": "1.0.0", "description": "Advanced Privacy Browser with Anti-Detection Features", "main": "dist/main.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start": "npm run build && electron .", "dev": "concurrently \"npm run build:watch\" \"wait-on dist/main.js && electron .\"", "pack": "electron-builder --dir", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:portable": "electron-builder --win portable", "dist:installer": "electron-builder --win nsis", "build-production": "npm run clean && npm run build && npm run dist:win", "test": "node test-suite.js", "clean": "rimraf dist release"}, "keywords": ["privacy", "browser", "anti-detection", "fingerprinting", "security"], "author": "Privacy Browser Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.8.0", "eslint": "^8.54.0", "rimraf": "^5.0.5", "typescript": "^5.3.0", "wait-on": "^7.2.0"}, "dependencies": {"axios": "^1.6.0", "crypto-js": "^4.2.0", "electron-store": "^8.2.0", "node-forge": "^1.3.1", "socks": "^2.7.1", "user-agents": "^1.0.1", "ws": "^8.14.0"}, "build": {"appId": "com.privacy.phantom-browser", "productName": "<PERSON> Browser", "directories": {"output": "release", "buildResources": "build"}, "files": ["dist/**/*", "renderer/**/*", "node_modules/**/*", "!node_modules/.cache", "!**/*.ts", "!**/*.map"], "extraResources": [{"from": "docs/", "to": "docs/", "filter": ["**/*"]}], "mac": {"category": "public.app-category.productivity", "icon": "build/icon.icns"}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}, {"target": "dir", "arch": ["x64"]}], "icon": "build/icon.ico", "sign": false, "requestedExecutionLevel": "asInvoker"}, "linux": {"target": "AppImage", "icon": "build/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "build/icon.ico", "uninstallerIcon": "build/icon.ico", "installerHeaderIcon": "build/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "<PERSON> Browser"}, "portable": {"artifactName": "PhantomBrowser-${version}-portable.exe"}, "compression": "maximum", "removePackageScripts": true}}